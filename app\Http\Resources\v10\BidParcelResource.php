<?php

namespace App\Http\Resources\v10;

use Illuminate\Http\Resources\Json\JsonResource;

class BidParcelResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            // Basic parcel information
            "id" => $this->id,
            "tracking_id" => $this->tracking_id,
            "merchant_id" => $this->merchant_id,
            "merchant" => [
                "id" => $this->merchant->id ?? null,
                "name" => $this->merchant->business_name ?? null,
                "user_name" => $this->merchant->user->name ?? null,
                "email" => $this->merchant->user->email ?? null,
                "mobile" => $this->merchant->user->mobile ?? null,
                "address" => $this->merchant->address ?? null
            ],
            "customer_name" => $this->customer_name,
            "customer_phone" => (string) $this->customer_phone,
            "customer_address" => $this->customer_address,
            "pickup_address" => $this->pickup_address,
            "pickup_phone" => $this->pickup_phone,
            "invoice_no" => (string) $this->invoice_no,
            "weight" => (string) $this->weight . ' ' . optional($this->deliveryCategory)->title,
            "category" => [
                "id" => $this->category_id,
                "title" => optional($this->deliveryCategory)->title
            ],
            "delivery_type" => [
                "id" => $this->delivery_type_id,
                "name" => trans("deliveryType." . $this->delivery_type_id)
            ],
            "cash_collection" => $this->cash_collection,
            "cod_amount" => $this->cod_amount,
            "note" => $this->note,
            
            // Bidding specific information
            "is_bid_parcel" => true,
            "bid_status" => $this->bid_status,
            "offered_delivery_charge" => (float) $this->offered_delivery_charge,
            "accepted_delivery_charge" => (float) $this->accepted_delivery_charge,
            "minimum_bid_amount" => (float) $this->minimum_bid_amount,
            "maximum_bid_amount" => (float) $this->maximum_bid_amount,
            "bid_requirements" => $this->bid_requirements,
            "estimated_pickup_time" => $this->estimated_pickup_time,
            "bid_timeout_at" => $this->bid_timeout_at?->format('Y-m-d H:i:s'),
            "time_remaining_hours" => $this->bid_timeout_at ? 
                max(0, now()->diffInHours($this->bid_timeout_at, false)) : 0,
            "is_expired" => $this->bid_timeout_at ? now()->isAfter($this->bid_timeout_at) : false,
            
            // Bid statistics
            "bids_summary" => [
                "total_bids" => $this->companyBids()->count(),
                "active_bids" => $this->companyBids()->where('status', 'pending')->count(),
                "lowest_bid" => $this->companyBids()->where('status', 'pending')->min('offered_charge'),
                "highest_bid" => $this->companyBids()->where('status', 'pending')->max('offered_charge'),
                "average_bid" => $this->companyBids()->where('status', 'pending')->avg('offered_charge')
            ],
            
            // Company bids (if requested)
            "company_bids" => $this->when($request->get('include_bids'), function() {
                return $this->companyBids->map(function($bid) {
                    return [
                        "id" => $bid->id,
                        "company" => [
                            "id" => $bid->company->id ?? null,
                            "name" => $bid->company->name ?? null,
                            "phone" => $bid->company->phone ?? null
                        ],
                        "offered_charge" => (float) $bid->offered_charge,
                        "estimated_pickup_time" => $bid->estimated_pickup_time,
                        "estimated_delivery_time" => $bid->estimated_delivery_time,
                        "message" => $bid->message,
                        "status" => $bid->status,
                        "created_at" => $bid->created_at->format('Y-m-d H:i:s'),
                        "can_be_accepted" => $bid->canBeAccepted(),
                        "can_be_withdrawn" => $bid->canBeWithdrawn()
                    ];
                });
            }),
            
            // Winning company information
            "winning_company" => $this->when($this->winning_company_id, function() {
                return [
                    "id" => $this->winningCompany->id ?? null,
                    "name" => $this->winningCompany->name ?? null,
                    "phone" => $this->winningCompany->phone ?? null,
                    "email" => $this->winningCompany->email ?? null,
                    "address" => $this->winningCompany->address ?? null
                ];
            }),
            
            // Assigned delivery man information
            "assigned_deliveryman" => $this->when($this->assigned_deliveryman_id, function() {
                return [
                    "id" => $this->assignedDeliveryman->id ?? null,
                    "name" => $this->assignedDeliveryman->name ?? null,
                    "phone" => $this->assignedDeliveryman->phone ?? null,
                    "email" => $this->assignedDeliveryman->email ?? null,
                    "vehicle_type" => $this->assignedDeliveryman->vehicle_type ?? null,
                    "vehicle_number" => $this->assignedDeliveryman->vehicle_number ?? null,
                    "current_location" => [
                        "lat" => $this->assignedDeliveryman->current_lat ?? null,
                        "lng" => $this->assignedDeliveryman->current_lng ?? null,
                        "last_update" => $this->assignedDeliveryman->last_location_update ?? null
                    ],
                    "average_rating" => $this->assignedDeliveryman->average_rating ?? 5.0,
                    "total_deliveries" => $this->assignedDeliveryman->completed_deliveries_count ?? 0
                ];
            }),
            
            // Status information
            "status" => (int) $this->status,
            "status_name" => trans("parcelStatus." . $this->status),
            "status_badge" => $this->getBidStatusBadgeAttribute(),
            
            // Dates
            "pickup_date" => dateFormat($this->pickup_date),
            "delivery_date" => dateFormat($this->delivery_date),
            "created_at" => $this->created_at->format('Y-m-d H:i:s'),
            "updated_at" => $this->updated_at->format('Y-m-d H:i:s'),
            "parcel_date" => dateFormat($this->created_at),
            "parcel_time" => date('h:i a', strtotime($this->created_at)),
            
            // Hub information
            "hub" => $this->when($this->hub_id, function() {
                return [
                    "id" => $this->hub->id ?? null,
                    "name" => $this->hub->name ?? null,
                    "address" => $this->hub->address ?? null
                ];
            }),
            
            // Actions available
            "available_actions" => $this->getAvailableActions(),
            
            // Notifications count
            "notifications_sent" => $this->bidNotifications()->count(),
            "last_notification_at" => $this->bidNotifications()->latest()->first()?->created_at?->format('Y-m-d H:i:s')
        ];
    }
    
    /**
     * Get available actions for the parcel based on current state
     */
    private function getAvailableActions()
    {
        $actions = [];
        
        switch ($this->bid_status) {
            case 'open':
                $actions[] = 'accept_bid';
                $actions[] = 'cancel_bidding';
                if (!$this->bid_timeout_at || now()->isBefore($this->bid_timeout_at)) {
                    $actions[] = 'extend_timeout';
                }
                break;
                
            case 'closed':
                if (!$this->assigned_deliveryman_id) {
                    $actions[] = 'assign_deliveryman';
                }
                break;
                
            case 'assigned':
                $actions[] = 'track_delivery';
                $actions[] = 'update_status';
                break;
                
            case 'expired':
                $actions[] = 'reopen_bidding';
                $actions[] = 'convert_to_normal';
                break;
        }
        
        return $actions;
    }
}
