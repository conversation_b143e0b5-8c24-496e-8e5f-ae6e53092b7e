<?php

namespace App\Services;

use App\Models\Backend\Parcel;
use App\Models\Backend\ParcelEvent;
use App\Enums\ParcelStatus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class ParcelStatusService
{
    /**
     * Update parcel status with proper validation and logging
     */
    public function updateStatus($parcelId, $newStatus, $data = [])
    {
        try {
            DB::beginTransaction();

            $parcel = Parcel::find($parcelId);
            if (!$parcel) {
                throw new \Exception('Parcel not found');
            }

            $oldStatus = $parcel->status;

            // Validate status transition
            if (!$this->isValidStatusTransition($parcel, $newStatus)) {
                throw new \Exception("Invalid status transition from {$oldStatus} to {$newStatus}");
            }

            // Handle bidding-specific status updates
            if ($parcel->is_bid_parcel) {
                $this->handleBidParcelStatusUpdate($parcel, $newStatus, $data);
            } else {
                $this->handleNormalParcelStatusUpdate($parcel, $newStatus, $data);
            }

            // Create parcel event log
            $this->createParcelEvent($parcel, $newStatus, $data);

            DB::commit();

            return [
                'success' => true,
                'parcel' => $parcel->fresh(),
                'old_status' => $oldStatus,
                'new_status' => $newStatus,
                'message' => 'Status updated successfully'
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update parcel status: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Handle bid parcel status updates
     */
    private function handleBidParcelStatusUpdate($parcel, $newStatus, $data)
    {
        switch ($newStatus) {
            case ParcelStatus::BID_OPEN:
                $parcel->update([
                    'status' => $newStatus,
                    'bid_status' => 'open'
                ]);
                break;

            case ParcelStatus::BID_CLOSED:
                $parcel->update([
                    'status' => $newStatus,
                    'bid_status' => 'closed'
                ]);
                break;

            case ParcelStatus::BID_EXPIRED:
                $parcel->update([
                    'status' => $newStatus,
                    'bid_status' => 'expired'
                ]);
                break;

            case ParcelStatus::COMPANY_ASSIGNED:
                $parcel->update([
                    'status' => $newStatus,
                    'bid_status' => 'assigned',
                    'winning_company_id' => $data['company_id'] ?? null
                ]);
                break;

            case ParcelStatus::PICKUP_ASSIGN:
                // For bid parcels, this means company has assigned a delivery man
                $parcel->update([
                    'status' => $newStatus,
                    'assigned_deliveryman_id' => $data['deliveryman_id'] ?? null
                ]);
                break;

            default:
                // Handle normal status updates
                $parcel->update(['status' => $newStatus]);
                break;
        }
    }

    /**
     * Handle normal parcel status updates
     */
    private function handleNormalParcelStatusUpdate($parcel, $newStatus, $data)
    {
        switch ($newStatus) {
            case ParcelStatus::PICKUP_ASSIGN:
                $parcel->update([
                    'status' => $newStatus,
                    'assigned_deliveryman_id' => $data['deliveryman_id'] ?? null
                ]);
                break;

            case ParcelStatus::DELIVERY_MAN_ASSIGN:
                $parcel->update([
                    'status' => $newStatus,
                    'assigned_deliveryman_id' => $data['deliveryman_id'] ?? null
                ]);
                break;

            case ParcelStatus::DELIVERED:
                $parcel->update([
                    'status' => $newStatus,
                    'delivery_date' => $data['delivery_date'] ?? Carbon::now()
                ]);
                break;

            default:
                $parcel->update(['status' => $newStatus]);
                break;
        }
    }

    /**
     * Validate if status transition is allowed
     */
    private function isValidStatusTransition($parcel, $newStatus)
    {
        $currentStatus = $parcel->status;

        // Define valid transitions for bid parcels
        if ($parcel->is_bid_parcel) {
            $bidTransitions = [
                ParcelStatus::PENDING => [
                    ParcelStatus::BID_OPEN,
                    ParcelStatus::BID_EXPIRED
                ],
                ParcelStatus::BID_OPEN => [
                    ParcelStatus::BID_CLOSED,
                    ParcelStatus::BID_EXPIRED
                ],
                ParcelStatus::BID_CLOSED => [
                    ParcelStatus::COMPANY_ASSIGNED,
                    ParcelStatus::BID_EXPIRED
                ],
                ParcelStatus::COMPANY_ASSIGNED => [
                    ParcelStatus::PICKUP_ASSIGN
                ],
                ParcelStatus::PICKUP_ASSIGN => [
                    ParcelStatus::RECEIVED_BY_PICKUP_MAN,
                    ParcelStatus::PICKUP_RE_SCHEDULE
                ],
                ParcelStatus::RECEIVED_BY_PICKUP_MAN => [
                    ParcelStatus::RECEIVED_WAREHOUSE,
                    ParcelStatus::TRANSFER_TO_HUB
                ],
                ParcelStatus::RECEIVED_WAREHOUSE => [
                    ParcelStatus::TRANSFER_TO_HUB,
                    ParcelStatus::DELIVERY_MAN_ASSIGN
                ],
                ParcelStatus::TRANSFER_TO_HUB => [
                    ParcelStatus::RECEIVED_BY_HUB
                ],
                ParcelStatus::RECEIVED_BY_HUB => [
                    ParcelStatus::DELIVERY_MAN_ASSIGN
                ],
                ParcelStatus::DELIVERY_MAN_ASSIGN => [
                    ParcelStatus::DELIVERED,
                    ParcelStatus::DELIVERY_RE_SCHEDULE,
                    ParcelStatus::PARTIAL_DELIVERED
                ],
                ParcelStatus::DELIVERY_RE_SCHEDULE => [
                    ParcelStatus::DELIVERED,
                    ParcelStatus::DELIVERY_MAN_ASSIGN
                ],
                ParcelStatus::PARTIAL_DELIVERED => [
                    ParcelStatus::DELIVERED
                ]
            ];

            return isset($bidTransitions[$currentStatus]) && 
                   in_array($newStatus, $bidTransitions[$currentStatus]);
        }

        // Define valid transitions for normal parcels
        $normalTransitions = [
            ParcelStatus::PENDING => [
                ParcelStatus::PICKUP_ASSIGN
            ],
            ParcelStatus::PICKUP_ASSIGN => [
                ParcelStatus::RECEIVED_BY_PICKUP_MAN,
                ParcelStatus::PICKUP_RE_SCHEDULE
            ],
            ParcelStatus::RECEIVED_BY_PICKUP_MAN => [
                ParcelStatus::RECEIVED_WAREHOUSE,
                ParcelStatus::TRANSFER_TO_HUB
            ],
            ParcelStatus::RECEIVED_WAREHOUSE => [
                ParcelStatus::TRANSFER_TO_HUB,
                ParcelStatus::DELIVERY_MAN_ASSIGN
            ],
            ParcelStatus::TRANSFER_TO_HUB => [
                ParcelStatus::RECEIVED_BY_HUB
            ],
            ParcelStatus::RECEIVED_BY_HUB => [
                ParcelStatus::DELIVERY_MAN_ASSIGN
            ],
            ParcelStatus::DELIVERY_MAN_ASSIGN => [
                ParcelStatus::DELIVERED,
                ParcelStatus::DELIVERY_RE_SCHEDULE,
                ParcelStatus::PARTIAL_DELIVERED
            ],
            ParcelStatus::DELIVERY_RE_SCHEDULE => [
                ParcelStatus::DELIVERED,
                ParcelStatus::DELIVERY_MAN_ASSIGN
            ],
            ParcelStatus::PARTIAL_DELIVERED => [
                ParcelStatus::DELIVERED
            ]
        ];

        return isset($normalTransitions[$currentStatus]) && 
               in_array($newStatus, $normalTransitions[$currentStatus]);
    }

    /**
     * Create parcel event log
     */
    private function createParcelEvent($parcel, $status, $data)
    {
        $event = new ParcelEvent();
        $event->parcel_id = $parcel->id;
        $event->parcel_status = $status;
        $event->note = $data['note'] ?? null;
        $event->created_by = Auth::id() ?? 1;

        // Set specific fields based on status
        switch ($status) {
            case ParcelStatus::PICKUP_ASSIGN:
                $event->pickup_man_id = $data['deliveryman_id'] ?? null;
                break;

            case ParcelStatus::DELIVERY_MAN_ASSIGN:
                $event->delivery_man_id = $data['deliveryman_id'] ?? null;
                break;

            case ParcelStatus::DELIVERED:
                $event->delivery_man_id = $parcel->assigned_deliveryman_id;
                $event->delivery_lat = $data['delivery_lat'] ?? null;
                $event->delivery_long = $data['delivery_lng'] ?? null;
                break;
        }

        $event->save();

        return $event;
    }

    /**
     * Get status history for a parcel
     */
    public function getStatusHistory($parcelId)
    {
        $parcel = Parcel::find($parcelId);
        if (!$parcel) {
            throw new \Exception('Parcel not found');
        }

        $events = ParcelEvent::where('parcel_id', $parcelId)
            ->with(['pickupMan', 'deliveryMan', 'createdBy'])
            ->orderBy('created_at', 'asc')
            ->get();

        return [
            'parcel' => $parcel,
            'events' => $events->map(function($event) {
                return [
                    'id' => $event->id,
                    'status' => $event->parcel_status,
                    'status_name' => $this->getStatusName($event->parcel_status),
                    'note' => $event->note,
                    'pickup_man' => $event->pickupMan ? [
                        'id' => $event->pickupMan->id,
                        'name' => $event->pickupMan->name
                    ] : null,
                    'delivery_man' => $event->deliveryMan ? [
                        'id' => $event->deliveryMan->id,
                        'name' => $event->deliveryMan->name
                    ] : null,
                    'created_by' => $event->createdBy ? [
                        'id' => $event->createdBy->id,
                        'name' => $event->createdBy->name
                    ] : null,
                    'created_at' => $event->created_at
                ];
            })
        ];
    }

    /**
     * Get human-readable status name
     */
    private function getStatusName($status)
    {
        $statusNames = [
            ParcelStatus::PENDING => 'Pending',
            ParcelStatus::PICKUP_ASSIGN => 'Pickup Assigned',
            ParcelStatus::PICKUP_RE_SCHEDULE => 'Pickup Rescheduled',
            ParcelStatus::RECEIVED_BY_PICKUP_MAN => 'Received by Pickup Man',
            ParcelStatus::RECEIVED_WAREHOUSE => 'Received at Warehouse',
            ParcelStatus::TRANSFER_TO_HUB => 'Transfer to Hub',
            ParcelStatus::DELIVERY_MAN_ASSIGN => 'Delivery Man Assigned',
            ParcelStatus::DELIVERY_RE_SCHEDULE => 'Delivery Rescheduled',
            ParcelStatus::DELIVERED => 'Delivered',
            ParcelStatus::PARTIAL_DELIVERED => 'Partially Delivered',
            
            // Bidding statuses
            ParcelStatus::BID_PENDING => 'Bid Pending',
            ParcelStatus::BID_OPEN => 'Bid Open',
            ParcelStatus::BID_CLOSED => 'Bid Closed',
            ParcelStatus::BID_EXPIRED => 'Bid Expired',
            ParcelStatus::BID_ASSIGNED => 'Bid Assigned',
            ParcelStatus::COMPANY_ASSIGNED => 'Company Assigned'
        ];

        return $statusNames[$status] ?? 'Unknown Status';
    }

    /**
     * Get next possible statuses for a parcel
     */
    public function getNextPossibleStatuses($parcelId)
    {
        $parcel = Parcel::find($parcelId);
        if (!$parcel) {
            throw new \Exception('Parcel not found');
        }

        $currentStatus = $parcel->status;
        $possibleStatuses = [];

        if ($parcel->is_bid_parcel) {
            // Get possible transitions for bid parcels
            $bidTransitions = [
                ParcelStatus::PENDING => [ParcelStatus::BID_OPEN, ParcelStatus::BID_EXPIRED],
                ParcelStatus::BID_OPEN => [ParcelStatus::BID_CLOSED, ParcelStatus::BID_EXPIRED],
                ParcelStatus::BID_CLOSED => [ParcelStatus::COMPANY_ASSIGNED],
                ParcelStatus::COMPANY_ASSIGNED => [ParcelStatus::PICKUP_ASSIGN],
                // ... add more as needed
            ];

            $possibleStatuses = $bidTransitions[$currentStatus] ?? [];
        } else {
            // Get possible transitions for normal parcels
            $normalTransitions = [
                ParcelStatus::PENDING => [ParcelStatus::PICKUP_ASSIGN],
                ParcelStatus::PICKUP_ASSIGN => [ParcelStatus::RECEIVED_BY_PICKUP_MAN, ParcelStatus::PICKUP_RE_SCHEDULE],
                // ... add more as needed
            ];

            $possibleStatuses = $normalTransitions[$currentStatus] ?? [];
        }

        return array_map(function($status) {
            return [
                'id' => $status,
                'name' => $this->getStatusName($status)
            ];
        }, $possibleStatuses);
    }
}
