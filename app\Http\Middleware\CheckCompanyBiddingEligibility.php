<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Backend\GeneralSettings;
use App\Helpers\Bidding\BiddingSettingsHelper;

class CheckCompanyBiddingEligibility
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication required',
                'error_code' => 'UNAUTHENTICATED'
            ], 401);
        }

        // Get company from user (adjust based on your auth structure)
        $company = $this->getCurrentCompany($user);
        
        if (!$company) {
            return response()->json([
                'success' => false,
                'message' => 'Company not found',
                'error_code' => 'COMPANY_NOT_FOUND'
            ], 404);
        }

        // Check if company has bidding enabled
        if (!$company->bidding_enabled) {
            return response()->json([
                'success' => false,
                'message' => 'Bidding is disabled for your company',
                'error_code' => 'COMPANY_BIDDING_DISABLED'
            ], 403);
        }

        // Check if company is in a hub
        if (!$company->hub_id) {
            return response()->json([
                'success' => false,
                'message' => 'Company must be assigned to a hub for bidding',
                'error_code' => 'NO_HUB_ASSIGNED'
            ], 403);
        }

        // Check if company has minimum required delivery men
        $minDeliveryMen = BiddingSettingsHelper::get('min_delivery_men_per_company', 1);
        $availableDeliveryMen = $company->getAvailableDeliveryMenCount();
        
        if ($availableDeliveryMen < $minDeliveryMen) {
            return response()->json([
                'success' => false,
                'message' => "Company must have at least {$minDeliveryMen} available delivery men for bidding",
                'error_code' => 'INSUFFICIENT_DELIVERY_MEN',
                'required' => $minDeliveryMen,
                'available' => $availableDeliveryMen
            ], 403);
        }

        // Check rate limiting
        if ($this->isRateLimited($company)) {
            return response()->json([
                'success' => false,
                'message' => 'Rate limit exceeded. Please try again later.',
                'error_code' => 'RATE_LIMITED'
            ], 429);
        }

        // Add company to request for use in controllers
        $request->merge(['current_company' => $company]);

        return $next($request);
    }

    /**
     * Get current company from user
     */
    private function getCurrentCompany($user)
    {
        // This might need adjustment based on your authentication structure
        return $user->company ?? GeneralSettings::first();
    }

    /**
     * Check if company is rate limited
     */
    private function isRateLimited($company)
    {
        $rateLimit = BiddingSettingsHelper::get('bid_rate_limit_per_hour', 20);
        $recentBids = $company->companyBids()
            ->where('created_at', '>=', now()->subHour())
            ->count();

        return $recentBids >= $rateLimit;
    }
}
