<?php

namespace App\Helpers\Bidding;

use App\Models\Backend\GeneralSettings;
use App\Models\Backend\Parcel;
use App\Models\Backend\DeliveryMan;
use App\Models\Backend\CompanyBid;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class CompanyBiddingHelper
{
    /**
     * Validate if a company can place a bid on a parcel
     */
    public static function validateCompanyBid($companyId, $parcelId, $bidAmount)
    {
        $company = GeneralSettings::find($companyId);
        $parcel = Parcel::find($parcelId);

        if (!$company || !$parcel) {
            return [
                'valid' => false,
                'message' => 'Company or parcel not found'
            ];
        }

        // Check if company can bid on this parcel
        if (!$company->canBidOnParcel($parcel)) {
            return [
                'valid' => false,
                'message' => 'Company cannot bid on this parcel'
            ];
        }

        // Check bid amount against company limits
        if ($bidAmount < $company->min_bid_amount) {
            return [
                'valid' => false,
                'message' => "Bid amount must be at least {$company->min_bid_amount}"
            ];
        }

        if ($bidAmount > $company->max_bid_amount) {
            return [
                'valid' => false,
                'message' => "Bid amount cannot exceed {$company->max_bid_amount}"
            ];
        }

        // Check parcel bid limits
        if ($parcel->minimum_bid_amount && $bidAmount < $parcel->minimum_bid_amount) {
            return [
                'valid' => false,
                'message' => "Bid amount must be at least {$parcel->minimum_bid_amount}"
            ];
        }

        if ($parcel->maximum_bid_amount && $bidAmount > $parcel->maximum_bid_amount) {
            return [
                'valid' => false,
                'message' => "Bid amount cannot exceed {$parcel->maximum_bid_amount}"
            ];
        }

        return [
            'valid' => true,
            'message' => 'Bid is valid'
        ];
    }

    /**
     * Calculate company's current capacity for taking new deliveries
     */
    public static function calculateCompanyCapacity($companyId)
    {
        $company = GeneralSettings::find($companyId);
        if (!$company) {
            return 0;
        }

        $totalDeliveryMen = $company->deliveryMen()->where('status', 1)->count();
        $availableDeliveryMen = $company->deliveryMen()
            ->where('status', 1)
            ->where('currently_available', true)
            ->count();

        $activeParcels = $company->wonParcels()
            ->whereIn('status', ['pickup_assigned', 'pickup_in_progress', 'in_transit'])
            ->count();

        $pendingBids = $company->companyBids()
            ->where('status', 'pending')
            ->count();

        return [
            'total_delivery_men' => $totalDeliveryMen,
            'available_delivery_men' => $availableDeliveryMen,
            'active_parcels' => $activeParcels,
            'pending_bids' => $pendingBids,
            'capacity_percentage' => $totalDeliveryMen > 0 ? 
                round(($availableDeliveryMen / $totalDeliveryMen) * 100, 2) : 0
        ];
    }

    /**
     * Get available delivery men for a company
     */
    public static function getCompanyDeliverymen($companyId, $available = true)
    {
        $query = DeliveryMan::where('company_id', $companyId)
            ->where('status', 1); // Active

        if ($available) {
            $query->where('currently_available', true);
        }

        return $query->get();
    }

    /**
     * Estimate delivery time based on distance and company performance
     */
    public static function estimateDeliveryTime($companyId, $pickupLat, $pickupLng, $deliveryLat, $deliveryLng)
    {
        $company = GeneralSettings::find($companyId);
        if (!$company) {
            return null;
        }

        // Calculate distance (simplified - in real implementation use Google Maps API)
        $distance = self::calculateDistance($pickupLat, $pickupLng, $deliveryLat, $deliveryLng);

        // Base time calculation (assuming 30 km/h average speed)
        $baseDeliveryTime = ($distance / 30) * 60; // minutes

        // Add company's default pickup time
        $totalTime = $baseDeliveryTime + $company->default_pickup_time;

        // Adjust based on company performance (if available)
        $performanceMultiplier = self::getCompanyPerformanceMultiplier($companyId);
        $estimatedTime = $totalTime * $performanceMultiplier;

        return [
            'distance_km' => round($distance, 2),
            'estimated_pickup_time' => $company->default_pickup_time,
            'estimated_delivery_time' => round($estimatedTime, 0),
            'total_time_minutes' => round($estimatedTime + $company->default_pickup_time, 0)
        ];
    }

    /**
     * Calculate profit margin for a bid
     */
    public static function calculateProfitMargin($bidAmount, $estimatedCost)
    {
        if ($estimatedCost <= 0) {
            return 100; // If no cost, 100% profit
        }

        $profit = $bidAmount - $estimatedCost;
        $marginPercentage = ($profit / $bidAmount) * 100;

        return [
            'bid_amount' => $bidAmount,
            'estimated_cost' => $estimatedCost,
            'profit' => $profit,
            'margin_percentage' => round($marginPercentage, 2)
        ];
    }

    /**
     * Get company's bidding performance metrics
     */
    public static function getCompanyBiddingMetrics($companyId, $days = 30)
    {
        $company = GeneralSettings::find($companyId);
        if (!$company) {
            return null;
        }

        $startDate = Carbon::now()->subDays($days);

        $totalBids = $company->companyBids()
            ->where('created_at', '>=', $startDate)
            ->count();

        $wonBids = $company->companyBids()
            ->where('created_at', '>=', $startDate)
            ->where('status', 'accepted')
            ->count();

        $averageBidAmount = $company->companyBids()
            ->where('created_at', '>=', $startDate)
            ->avg('offered_charge');

        $averageWinningBidAmount = $company->companyBids()
            ->where('created_at', '>=', $startDate)
            ->where('status', 'accepted')
            ->avg('offered_charge');

        $successRate = $totalBids > 0 ? round(($wonBids / $totalBids) * 100, 2) : 0;

        return [
            'period_days' => $days,
            'total_bids' => $totalBids,
            'won_bids' => $wonBids,
            'success_rate' => $successRate,
            'average_bid_amount' => round($averageBidAmount, 2),
            'average_winning_bid_amount' => round($averageWinningBidAmount, 2)
        ];
    }

    /**
     * Get optimal bid amount suggestion
     */
    public static function suggestOptimalBidAmount($companyId, $parcelId)
    {
        $company = GeneralSettings::find($companyId);
        $parcel = Parcel::find($parcelId);

        if (!$company || !$parcel) {
            return null;
        }

        // Get existing bids for this parcel
        $existingBids = $parcel->companyBids()
            ->where('status', 'pending')
            ->orderBy('offered_charge', 'asc')
            ->get();

        $lowestBid = $existingBids->first();
        $averageBid = $existingBids->avg('offered_charge');

        // Calculate suggested bid based on various factors
        $baseBid = $parcel->offered_delivery_charge;
        
        // Adjust based on company's historical performance
        $metrics = self::getCompanyBiddingMetrics($companyId);
        $successRate = $metrics ? $metrics['success_rate'] : 50;

        // If success rate is low, suggest more competitive bid
        if ($successRate < 30) {
            $suggestedBid = $baseBid * 0.9; // 10% below offered amount
        } elseif ($successRate > 70) {
            $suggestedBid = $baseBid * 1.05; // 5% above offered amount
        } else {
            $suggestedBid = $baseBid; // Match offered amount
        }

        // Adjust based on existing competition
        if ($lowestBid && $suggestedBid >= $lowestBid->offered_charge) {
            $suggestedBid = $lowestBid->offered_charge - 1; // Beat lowest bid by 1 unit
        }

        // Ensure within company limits
        $suggestedBid = max($suggestedBid, $company->min_bid_amount);
        $suggestedBid = min($suggestedBid, $company->max_bid_amount);

        return [
            'suggested_bid' => round($suggestedBid, 2),
            'parcel_offered_amount' => $parcel->offered_delivery_charge,
            'lowest_competing_bid' => $lowestBid ? $lowestBid->offered_charge : null,
            'average_competing_bid' => round($averageBid, 2),
            'total_competing_bids' => $existingBids->count(),
            'company_success_rate' => $successRate
        ];
    }

    /**
     * Private helper methods
     */
    private static function calculateDistance($lat1, $lng1, $lat2, $lng2)
    {
        $earthRadius = 6371; // Earth's radius in kilometers

        $dLat = deg2rad($lat2 - $lat1);
        $dLng = deg2rad($lng2 - $lng1);

        $a = sin($dLat/2) * sin($dLat/2) +
             cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
             sin($dLng/2) * sin($dLng/2);

        $c = 2 * atan2(sqrt($a), sqrt(1-$a));
        $distance = $earthRadius * $c;

        return $distance;
    }

    private static function getCompanyPerformanceMultiplier($companyId)
    {
        // Get company's average delivery performance
        // This could be based on historical delivery times vs estimates
        // For now, return a default multiplier
        return 1.0;
    }
}
