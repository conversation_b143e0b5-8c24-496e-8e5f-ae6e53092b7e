<?php

namespace App\Http\Resources\v10;

use Illuminate\Http\Resources\Json\JsonResource;

class CompanyBidResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            "id" => $this->id,
            "parcel" => [
                "id" => $this->parcel->id,
                "tracking_id" => $this->parcel->tracking_id,
                "pickup_address" => $this->parcel->pickup_address,
                "customer_address" => $this->parcel->customer_address,
                "customer_name" => $this->parcel->customer_name,
                "weight" => $this->parcel->weight,
                "offered_delivery_charge" => $this->parcel->offered_delivery_charge,
                "minimum_bid_amount" => $this->parcel->minimum_bid_amount,
                "maximum_bid_amount" => $this->parcel->maximum_bid_amount,
                "bid_timeout_at" => $this->parcel->bid_timeout_at?->format('Y-m-d H:i:s'),
                "time_remaining_hours" => $this->parcel->bid_timeout_at ? 
                    max(0, now()->diffInHours($this->parcel->bid_timeout_at, false)) : 0,
                "merchant" => [
                    "id" => $this->parcel->merchant->id ?? null,
                    "name" => $this->parcel->merchant->business_name ?? null,
                    "phone" => $this->parcel->merchant->user->mobile ?? null
                ]
            ],
            "company" => [
                "id" => $this->company->id ?? null,
                "name" => $this->company->name ?? null,
                "phone" => $this->company->phone ?? null,
                "email" => $this->company->email ?? null
            ],
            "bid_details" => [
                "offered_charge" => (float) $this->offered_charge,
                "estimated_pickup_time" => $this->estimated_pickup_time,
                "estimated_delivery_time" => $this->estimated_delivery_time,
                "message" => $this->message,
                "contact_person" => $this->contact_person,
                "contact_phone" => $this->contact_phone,
                "contact_email" => $this->contact_email,
                "service_features" => $this->service_features ? json_decode($this->service_features, true) : [],
                "insurance_coverage" => (float) $this->insurance_coverage,
                "priority_delivery" => (bool) $this->priority_delivery,
                "terms_conditions" => $this->terms_conditions
            ],
            "status" => $this->status,
            "status_badge" => $this->getStatusBadge(),
            "created_at" => $this->created_at->format('Y-m-d H:i:s'),
            "updated_at" => $this->updated_at->format('Y-m-d H:i:s'),
            "accepted_at" => $this->accepted_at?->format('Y-m-d H:i:s'),
            "rejected_at" => $this->rejected_at?->format('Y-m-d H:i:s'),
            "withdrawn_at" => $this->withdrawn_at?->format('Y-m-d H:i:s'),
            
            // Competitive information
            "competition" => [
                "total_bids" => $this->parcel->companyBids()->count(),
                "active_bids" => $this->parcel->companyBids()->where('status', 'pending')->count(),
                "lowest_bid" => $this->parcel->companyBids()->where('status', 'pending')->min('offered_charge'),
                "highest_bid" => $this->parcel->companyBids()->where('status', 'pending')->max('offered_charge'),
                "my_rank" => $this->getBidRank(),
                "is_lowest" => $this->isLowestBid(),
                "is_highest" => $this->isHighestBid()
            ],
            
            // Actions available
            "can_be_accepted" => $this->canBeAccepted(),
            "can_be_withdrawn" => $this->canBeWithdrawn(),
            "can_be_modified" => $this->canBeModified(),
            
            // Time information
            "time_since_bid" => $this->created_at->diffForHumans(),
            "is_recent" => $this->created_at->isAfter(now()->subHours(1)),
            
            // Delivery information (if bid is accepted)
            "delivery_info" => $this->when($this->status === 'accepted', function() {
                return [
                    "assigned_deliveryman" => $this->parcel->assignedDeliveryman ? [
                        "id" => $this->parcel->assignedDeliveryman->id,
                        "name" => $this->parcel->assignedDeliveryman->name,
                        "phone" => $this->parcel->assignedDeliveryman->phone,
                        "current_location" => [
                            "lat" => $this->parcel->assignedDeliveryman->current_lat,
                            "lng" => $this->parcel->assignedDeliveryman->current_lng
                        ]
                    ] : null,
                    "pickup_date" => $this->parcel->pickup_date,
                    "delivery_date" => $this->parcel->delivery_date,
                    "current_status" => $this->parcel->status
                ];
            })
        ];
    }
    
    /**
     * Get status badge HTML
     */
    private function getStatusBadge()
    {
        $badges = [
            'pending' => '<span class="badge badge-warning">Pending</span>',
            'accepted' => '<span class="badge badge-success">Accepted</span>',
            'rejected' => '<span class="badge badge-danger">Rejected</span>',
            'withdrawn' => '<span class="badge badge-secondary">Withdrawn</span>',
            'expired' => '<span class="badge badge-dark">Expired</span>'
        ];
        
        return $badges[$this->status] ?? '<span class="badge badge-light">Unknown</span>';
    }
    
    /**
     * Get bid rank among all active bids
     */
    private function getBidRank()
    {
        if ($this->status !== 'pending') {
            return null;
        }
        
        $lowerBids = $this->parcel->companyBids()
            ->where('status', 'pending')
            ->where('offered_charge', '<', $this->offered_charge)
            ->count();
            
        return $lowerBids + 1;
    }
    
    /**
     * Check if this is the lowest bid
     */
    private function isLowestBid()
    {
        if ($this->status !== 'pending') {
            return false;
        }
        
        $lowestBid = $this->parcel->companyBids()
            ->where('status', 'pending')
            ->min('offered_charge');
            
        return $this->offered_charge == $lowestBid;
    }
    
    /**
     * Check if this is the highest bid
     */
    private function isHighestBid()
    {
        if ($this->status !== 'pending') {
            return false;
        }
        
        $highestBid = $this->parcel->companyBids()
            ->where('status', 'pending')
            ->max('offered_charge');
            
        return $this->offered_charge == $highestBid;
    }
}
