<?php

namespace App\Helpers\Bidding;

use App\Models\Backend\Parcel;
use App\Models\Backend\CompanyBid;
use App\Models\Backend\GeneralSettings;
use App\Models\Backend\BiddingSetting;
use App\Models\Backend\DeliveryCharge;
use Illuminate\Support\Facades\Log;

class BiddingCalculationHelper
{
    /**
     * Calculate minimum bid amount for a parcel
     */
    public static function calculateMinimumBid(Parcel $parcel)
    {
        try {
            $settings = BiddingSetting::getSettings();
            $baseCharge = $parcel->offered_delivery_charge ?? 0;
            
            // Apply minimum bid percentage
            $minBidPercentage = $settings['min_bid_percentage'] ?? 70;
            $minimumBid = $baseCharge * ($minBidPercentage / 100);
            
            // Ensure minimum bid doesn't go below absolute minimum
            $absoluteMinimum = $settings['absolute_minimum_bid'] ?? 10;
            
            return max($minimumBid, $absoluteMinimum);
        } catch (\Exception $e) {
            Log::error('Failed to calculate minimum bid: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Calculate maximum bid amount for a parcel
     */
    public static function calculateMaximumBid(Parcel $parcel)
    {
        try {
            $settings = BiddingSetting::getSettings();
            $baseCharge = $parcel->offered_delivery_charge ?? 0;
            
            // Apply maximum bid percentage
            $maxBidPercentage = $settings['max_bid_percentage'] ?? 95;
            $maximumBid = $baseCharge * ($maxBidPercentage / 100);
            
            return $maximumBid;
        } catch (\Exception $e) {
            Log::error('Failed to calculate maximum bid: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Validate bid amount
     */
    public static function validateBidAmount(Parcel $parcel, $bidAmount)
    {
        $minBid = self::calculateMinimumBid($parcel);
        $maxBid = self::calculateMaximumBid($parcel);
        
        if ($bidAmount < $minBid) {
            return [
                'valid' => false,
                'message' => "Bid amount must be at least {$minBid}",
                'min_bid' => $minBid,
                'max_bid' => $maxBid
            ];
        }
        
        if ($bidAmount > $maxBid) {
            return [
                'valid' => false,
                'message' => "Bid amount cannot exceed {$maxBid}",
                'min_bid' => $minBid,
                'max_bid' => $maxBid
            ];
        }
        
        return [
            'valid' => true,
            'message' => 'Bid amount is valid',
            'min_bid' => $minBid,
            'max_bid' => $maxBid
        ];
    }

    /**
     * Calculate distance-based pricing
     */
    public static function calculateDistanceBasedPrice($distance, $vehicleType = 'standard')
    {
        try {
            $settings = BiddingSetting::getSettings();
            
            // Base rates per km
            $baseRates = [
                'standard' => $settings['base_rate_per_km'] ?? 2.5,
                'express' => $settings['express_rate_per_km'] ?? 3.5,
                'premium' => $settings['premium_rate_per_km'] ?? 5.0
            ];
            
            $ratePerKm = $baseRates[$vehicleType] ?? $baseRates['standard'];
            
            // Calculate base price
            $basePrice = $distance * $ratePerKm;
            
            // Apply distance-based multipliers
            if ($distance > 50) {
                $basePrice *= 0.9; // 10% discount for long distance
            } elseif ($distance < 5) {
                $basePrice += $settings['minimum_distance_charge'] ?? 15; // Minimum charge for short distance
            }
            
            return round($basePrice, 2);
        } catch (\Exception $e) {
            Log::error('Failed to calculate distance-based price: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Calculate time-based pricing adjustments
     */
    public static function calculateTimeBasedAdjustment($basePrice, $pickupTime = null)
    {
        try {
            $settings = BiddingSetting::getSettings();
            $pickupHour = $pickupTime ? date('H', strtotime($pickupTime)) : date('H');
            
            $adjustment = 1.0; // Default multiplier
            
            // Peak hours (8-10 AM, 5-7 PM)
            if (($pickupHour >= 8 && $pickupHour <= 10) || ($pickupHour >= 17 && $pickupHour <= 19)) {
                $adjustment = $settings['peak_hour_multiplier'] ?? 1.2;
            }
            // Off-peak hours (10 PM - 6 AM)
            elseif ($pickupHour >= 22 || $pickupHour <= 6) {
                $adjustment = $settings['off_peak_multiplier'] ?? 1.3;
            }
            // Weekend adjustment
            elseif (in_array(date('w'), [0, 6])) { // Sunday = 0, Saturday = 6
                $adjustment = $settings['weekend_multiplier'] ?? 1.1;
            }
            
            return round($basePrice * $adjustment, 2);
        } catch (\Exception $e) {
            Log::error('Failed to calculate time-based adjustment: ' . $e->getMessage());
            return $basePrice;
        }
    }

    /**
     * Calculate company performance-based discount
     */
    public static function calculatePerformanceDiscount($companyId, $basePrice)
    {
        try {
            $company = GeneralSettings::find($companyId);
            if (!$company) {
                return $basePrice;
            }
            
            // Get company performance metrics
            $completionRate = self::getCompanyCompletionRate($companyId);
            $averageRating = self::getCompanyAverageRating($companyId);
            
            $discount = 0;
            
            // Completion rate bonus
            if ($completionRate >= 95) {
                $discount += 0.05; // 5% discount
            } elseif ($completionRate >= 90) {
                $discount += 0.03; // 3% discount
            }
            
            // Rating bonus
            if ($averageRating >= 4.5) {
                $discount += 0.03; // 3% discount
            } elseif ($averageRating >= 4.0) {
                $discount += 0.02; // 2% discount
            }
            
            // Cap maximum discount
            $discount = min($discount, 0.10); // Maximum 10% discount
            
            return round($basePrice * (1 - $discount), 2);
        } catch (\Exception $e) {
            Log::error('Failed to calculate performance discount: ' . $e->getMessage());
            return $basePrice;
        }
    }

    /**
     * Get company completion rate
     */
    private static function getCompanyCompletionRate($companyId)
    {
        try {
            $totalBids = CompanyBid::where('company_id', $companyId)
                ->where('status', 'accepted')
                ->where('created_at', '>=', now()->subDays(30))
                ->count();
                
            if ($totalBids == 0) {
                return 100; // New companies get benefit of doubt
            }
            
            $completedBids = CompanyBid::where('company_id', $companyId)
                ->where('status', 'accepted')
                ->where('created_at', '>=', now()->subDays(30))
                ->whereHas('parcel', function($query) {
                    $query->whereIn('status', ['delivered', 'partial_delivered']);
                })
                ->count();
                
            return round(($completedBids / $totalBids) * 100, 2);
        } catch (\Exception $e) {
            Log::error('Failed to get company completion rate: ' . $e->getMessage());
            return 100;
        }
    }

    /**
     * Get company average rating
     */
    private static function getCompanyAverageRating($companyId)
    {
        try {
            // This would need to be implemented based on your rating system
            // For now, return a default value
            return 4.0;
        } catch (\Exception $e) {
            Log::error('Failed to get company average rating: ' . $e->getMessage());
            return 4.0;
        }
    }

    /**
     * Calculate suggested bid amount for a company
     */
    public static function calculateSuggestedBid(Parcel $parcel, $companyId)
    {
        try {
            // Start with distance-based calculation
            $distance = $parcel->distance ?? 10; // Default distance if not set
            $basePrice = self::calculateDistanceBasedPrice($distance);
            
            // Apply time-based adjustments
            $adjustedPrice = self::calculateTimeBasedAdjustment($basePrice, $parcel->pickup_date);
            
            // Apply performance discount
            $finalPrice = self::calculatePerformanceDiscount($companyId, $adjustedPrice);
            
            // Ensure it's within bid limits
            $minBid = self::calculateMinimumBid($parcel);
            $maxBid = self::calculateMaximumBid($parcel);
            
            $suggestedBid = max($minBid, min($finalPrice, $maxBid));
            
            return [
                'suggested_amount' => round($suggestedBid, 2),
                'base_price' => round($basePrice, 2),
                'time_adjusted_price' => round($adjustedPrice, 2),
                'performance_adjusted_price' => round($finalPrice, 2),
                'min_bid' => round($minBid, 2),
                'max_bid' => round($maxBid, 2),
                'savings_potential' => round($parcel->offered_delivery_charge - $suggestedBid, 2)
            ];
        } catch (\Exception $e) {
            Log::error('Failed to calculate suggested bid: ' . $e->getMessage());
            return [
                'suggested_amount' => 0,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Calculate bid competitiveness score
     */
    public static function calculateBidCompetitiveness(CompanyBid $bid)
    {
        try {
            $parcel = $bid->parcel;
            $allBids = $parcel->companyBids()->where('status', 'pending')->get();
            
            if ($allBids->count() <= 1) {
                return 100; // Only bid or first bid
            }
            
            $sortedBids = $allBids->sortBy('offered_charge');
            $bidRank = $sortedBids->search(function($item) use ($bid) {
                return $item->id === $bid->id;
            }) + 1;
            
            // Calculate competitiveness as percentage
            $competitiveness = ((count($sortedBids) - $bidRank + 1) / count($sortedBids)) * 100;
            
            return round($competitiveness, 2);
        } catch (\Exception $e) {
            Log::error('Failed to calculate bid competitiveness: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Calculate estimated profit margin
     */
    public static function calculateProfitMargin($bidAmount, $estimatedCosts)
    {
        try {
            if ($bidAmount <= 0 || $estimatedCosts <= 0) {
                return 0;
            }
            
            $profit = $bidAmount - $estimatedCosts;
            $margin = ($profit / $bidAmount) * 100;
            
            return round($margin, 2);
        } catch (\Exception $e) {
            Log::error('Failed to calculate profit margin: ' . $e->getMessage());
            return 0;
        }
    }
}
