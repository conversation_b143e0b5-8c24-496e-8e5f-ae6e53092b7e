<?php

namespace App\Services;

use App\Models\Backend\Parcel;
use App\Models\Backend\DeliveryMan;
use App\Models\Backend\CompanyBid;
use App\Models\Backend\GeneralSettings;
use App\Enums\ParcelStatus;
use App\Helpers\Bidding\BiddingSettingsHelper;
use App\Services\HubBiddingService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ParcelAssignmentService
{
    protected $hubBiddingService;

    public function __construct(HubBiddingService $hubBiddingService)
    {
        $this->hubBiddingService = $hubBiddingService;
    }

    /**
     * Assign a parcel to delivery man (handles both normal and bid parcels)
     */
    public function assignParcel($parcelId, $deliverymanId, $companyId = null)
    {
        try {
            DB::beginTransaction();

            $parcel = Parcel::find($parcelId);
            if (!$parcel) {
                throw new \Exception('Parcel not found');
            }

            $deliveryman = DeliveryMan::find($deliverymanId);
            if (!$deliveryman) {
                throw new \Exception('Delivery man not found');
            }

            if ($parcel->is_bid_parcel) {
                // Handle bid parcel assignment
                return $this->assignBidParcel($parcel, $deliveryman, $companyId);
            } else {
                // Handle normal parcel assignment
                return $this->assignNormalParcel($parcel, $deliveryman);
            }

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to assign parcel: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Assign a normal (non-bid) parcel
     */
    private function assignNormalParcel($parcel, $deliveryman)
    {
        // Validate deliveryman availability
        if (!$this->isDeliverymanAvailable($deliveryman)) {
            throw new \Exception('Delivery man is not available');
        }

        // Update parcel
        $parcel->update([
            'assigned_deliveryman_id' => $deliveryman->id,
            'status' => ParcelStatus::PICKUP_ASSIGNED
        ]);

        // Update deliveryman availability
        $deliveryman->update(['currently_available' => false]);

        // Log assignment
        $this->logParcelAssignment($parcel, $deliveryman, 'normal');

        DB::commit();

        return [
            'success' => true,
            'parcel' => $parcel->fresh(),
            'deliveryman' => $deliveryman,
            'type' => 'normal',
            'message' => 'Parcel assigned successfully'
        ];
    }

    /**
     * Assign a bid parcel (only after bid is won)
     */
    private function assignBidParcel($parcel, $deliveryman, $companyId)
    {
        // Validate this is a won bid parcel
        if ($parcel->bid_status !== 'closed' || !$parcel->winning_company_id) {
            throw new \Exception('Parcel bid must be won before assignment');
        }

        // Validate company owns this parcel
        if ($parcel->winning_company_id !== $companyId) {
            throw new \Exception('Only winning company can assign delivery man');
        }

        // Validate deliveryman belongs to winning company
        if ($deliveryman->company_id !== $companyId) {
            throw new \Exception('Delivery man must belong to winning company');
        }

        // Validate deliveryman availability
        if (!$this->isDeliverymanAvailable($deliveryman)) {
            throw new \Exception('Delivery man is not available');
        }

        // Update parcel
        $parcel->update([
            'assigned_deliveryman_id' => $deliveryman->id,
            'bid_status' => 'assigned',
            'status' => ParcelStatus::PICKUP_ASSIGNED
        ]);

        // Update deliveryman availability
        $deliveryman->update(['currently_available' => false]);

        // Update deliveryman bid delivery count
        $deliveryman->increment('total_bid_deliveries');

        // Log assignment
        $this->logParcelAssignment($parcel, $deliveryman, 'bid');

        DB::commit();

        return [
            'success' => true,
            'parcel' => $parcel->fresh(),
            'deliveryman' => $deliveryman,
            'company' => $parcel->winningCompany,
            'type' => 'bid',
            'message' => 'Bid parcel assigned successfully'
        ];
    }

    /**
     * Auto-assign parcels based on criteria
     */
    public function autoAssignParcels($hubId = null, $companyId = null)
    {
        $assignedCount = 0;

        // Auto-assign normal parcels
        $normalParcels = Parcel::where('status', ParcelStatus::PENDING)
            ->where('is_bid_parcel', false)
            ->when($hubId, function($query, $hubId) {
                return $query->where('hub_id', $hubId);
            })
            ->limit(10)
            ->get();

        foreach ($normalParcels as $parcel) {
            $deliveryman = $this->findBestDeliverymanForParcel($parcel);
            if ($deliveryman) {
                try {
                    $this->assignNormalParcel($parcel, $deliveryman);
                    $assignedCount++;
                } catch (\Exception $e) {
                    Log::warning("Failed to auto-assign parcel {$parcel->id}: " . $e->getMessage());
                }
            }
        }

        // Auto-assign won bid parcels that haven't been assigned yet
        $wonBidParcels = Parcel::where('is_bid_parcel', true)
            ->where('bid_status', 'closed')
            ->whereNull('assigned_deliveryman_id')
            ->when($companyId, function($query, $companyId) {
                return $query->where('winning_company_id', $companyId);
            })
            ->limit(5)
            ->get();

        foreach ($wonBidParcels as $parcel) {
            $deliveryman = $this->findBestDeliverymanForBidParcel($parcel);
            if ($deliveryman) {
                try {
                    $this->assignBidParcel($parcel, $deliveryman, $parcel->winning_company_id);
                    $assignedCount++;
                } catch (\Exception $e) {
                    Log::warning("Failed to auto-assign bid parcel {$parcel->id}: " . $e->getMessage());
                }
            }
        }

        return $assignedCount;
    }

    /**
     * Find best delivery man for a normal parcel
     */
    private function findBestDeliverymanForParcel($parcel)
    {
        return DeliveryMan::where('status', 1)
            ->where('currently_available', true)
            ->where('available_for_bidding', true)
            ->when($parcel->hub_id, function($query, $hubId) {
                // Find delivery men in the same hub
                return $query->whereHas('company', function($q) use ($hubId) {
                    $q->where('hub_id', $hubId);
                });
            })
            ->orderBy('completed_deliveries_count', 'asc') // Least busy first
            ->first();
    }

    /**
     * Find best delivery man for a bid parcel
     */
    private function findBestDeliverymanForBidParcel($parcel)
    {
        return DeliveryMan::where('status', 1)
            ->where('currently_available', true)
            ->where('company_id', $parcel->winning_company_id)
            ->orderBy('average_rating', 'desc') // Best rated first
            ->orderBy('total_bid_deliveries', 'asc') // Least bid deliveries first
            ->first();
    }

    /**
     * Check if delivery man is available
     */
    private function isDeliverymanAvailable($deliveryman)
    {
        return $deliveryman->status == 1 && 
               $deliveryman->currently_available && 
               $deliveryman->available_for_bidding;
    }

    /**
     * Log parcel assignment
     */
    private function logParcelAssignment($parcel, $deliveryman, $type)
    {
        Log::info("Parcel assigned", [
            'parcel_id' => $parcel->id,
            'tracking_id' => $parcel->tracking_id,
            'deliveryman_id' => $deliveryman->id,
            'deliveryman_name' => $deliveryman->name,
            'company_id' => $deliveryman->company_id ?? null,
            'type' => $type,
            'is_bid_parcel' => $parcel->is_bid_parcel,
            'bid_status' => $parcel->bid_status ?? null,
            'assigned_at' => Carbon::now()
        ]);
    }

    /**
     * Unassign a parcel (make delivery man available again)
     */
    public function unassignParcel($parcelId, $reason = null)
    {
        try {
            DB::beginTransaction();

            $parcel = Parcel::find($parcelId);
            if (!$parcel || !$parcel->assigned_deliveryman_id) {
                throw new \Exception('Parcel not found or not assigned');
            }

            $deliveryman = $parcel->assignedDeliveryman;

            // Update parcel
            $parcel->update([
                'assigned_deliveryman_id' => null,
                'status' => $parcel->is_bid_parcel ? ParcelStatus::PENDING : ParcelStatus::PENDING
            ]);

            // Make deliveryman available again
            if ($deliveryman) {
                $deliveryman->update(['currently_available' => true]);
            }

            // Log unassignment
            Log::info("Parcel unassigned", [
                'parcel_id' => $parcel->id,
                'tracking_id' => $parcel->tracking_id,
                'deliveryman_id' => $deliveryman->id ?? null,
                'reason' => $reason,
                'unassigned_at' => Carbon::now()
            ]);

            DB::commit();

            return [
                'success' => true,
                'message' => 'Parcel unassigned successfully'
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to unassign parcel: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get assignment statistics
     */
    public function getAssignmentStatistics($companyId = null, $days = 30)
    {
        $startDate = Carbon::now()->subDays($days);

        $query = Parcel::where('created_at', '>=', $startDate);
        
        if ($companyId) {
            $query->where(function($q) use ($companyId) {
                $q->where('winning_company_id', $companyId)
                  ->orWhereHas('assignedDeliveryman', function($subQ) use ($companyId) {
                      $subQ->where('company_id', $companyId);
                  });
            });
        }

        $totalParcels = $query->count();
        $assignedParcels = $query->whereNotNull('assigned_deliveryman_id')->count();
        $bidParcels = $query->where('is_bid_parcel', true)->count();
        $normalParcels = $query->where('is_bid_parcel', false)->count();

        return [
            'period_days' => $days,
            'total_parcels' => $totalParcels,
            'assigned_parcels' => $assignedParcels,
            'unassigned_parcels' => $totalParcels - $assignedParcels,
            'assignment_rate' => $totalParcels > 0 ? round(($assignedParcels / $totalParcels) * 100, 2) : 0,
            'bid_parcels' => $bidParcels,
            'normal_parcels' => $normalParcels,
            'bid_percentage' => $totalParcels > 0 ? round(($bidParcels / $totalParcels) * 100, 2) : 0
        ];
    }
}
