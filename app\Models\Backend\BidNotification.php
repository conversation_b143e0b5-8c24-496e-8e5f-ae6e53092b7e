<?php

namespace App\Models\Backend;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class BidNotification extends Model
{
    use HasFactory;

    protected $table = 'bid_notifications';

    protected $fillable = [
        'company_id',
        'parcel_id',
        'company_bid_id',
        'notification_type',
        'title',
        'message',
        'data',
        'sent_push',
        'sent_email',
        'sent_sms',
        'sent_at',
        'read_at',
        'clicked_at',
        'retry_count',
        'next_retry_at',
        'error_message'
    ];

    protected $casts = [
        'data' => 'array',
        'sent_push' => 'boolean',
        'sent_email' => 'boolean',
        'sent_sms' => 'boolean',
        'sent_at' => 'datetime',
        'read_at' => 'datetime',
        'clicked_at' => 'datetime',
        'next_retry_at' => 'datetime',
    ];

    /**
     * Relationships
     */
    public function company()
    {
        return $this->belongsTo(GeneralSettings::class, 'company_id', 'id');
    }

    public function parcel()
    {
        return $this->belongsTo(Parcel::class, 'parcel_id', 'id');
    }

    public function companyBid()
    {
        return $this->belongsTo(CompanyBid::class, 'company_bid_id', 'id');
    }

    /**
     * Scopes
     */
    public function scopeUnread($query)
    {
        return $query->whereNull('read_at');
    }

    public function scopeRead($query)
    {
        return $query->whereNotNull('read_at');
    }

    public function scopeForCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    public function scopePendingRetry($query)
    {
        return $query->whereNotNull('next_retry_at')
            ->where('next_retry_at', '<=', Carbon::now())
            ->where('retry_count', '<', 3);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('notification_type', $type);
    }

    /**
     * Accessors
     */
    public function getIsReadAttribute()
    {
        return !is_null($this->read_at);
    }

    public function getIsClickedAttribute()
    {
        return !is_null($this->clicked_at);
    }

    public function getTimeSinceCreatedAttribute()
    {
        return $this->created_at->diffForHumans();
    }

    /**
     * Business Logic Methods
     */
    public function markAsRead()
    {
        if (!$this->is_read) {
            $this->update(['read_at' => Carbon::now()]);
        }
    }

    public function markAsClicked()
    {
        if (!$this->is_clicked) {
            $this->update(['clicked_at' => Carbon::now()]);
        }
        
        // Also mark as read if not already
        $this->markAsRead();
    }

    public function markAsSent($channel = null)
    {
        $updates = ['sent_at' => Carbon::now()];
        
        if ($channel) {
            $updates["sent_{$channel}"] = true;
        }
        
        $this->update($updates);
    }

    public function incrementRetry($errorMessage = null)
    {
        $this->update([
            'retry_count' => $this->retry_count + 1,
            'next_retry_at' => Carbon::now()->addMinutes(pow(2, $this->retry_count)), // Exponential backoff
            'error_message' => $errorMessage
        ]);
    }

    public function canRetry()
    {
        return $this->retry_count < 3 && 
               $this->next_retry_at && 
               Carbon::now()->isAfter($this->next_retry_at);
    }

    /**
     * Static Methods
     */
    public static function createForNewBid($parcel, $companies)
    {
        $notifications = [];
        
        foreach ($companies as $company) {
            $notifications[] = [
                'company_id' => $company->id,
                'parcel_id' => $parcel->id,
                'notification_type' => 'new_bid_available',
                'title' => 'New Bid Available',
                'message' => "New parcel available for bidding: {$parcel->tracking_id}",
                'data' => [
                    'parcel_id' => $parcel->id,
                    'tracking_id' => $parcel->tracking_id,
                    'pickup_address' => $parcel->pickup_address,
                    'delivery_address' => $parcel->customer_address,
                    'offered_charge' => $parcel->offered_delivery_charge
                ],
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ];
        }
        
        return self::insert($notifications);
    }

    public static function createForBidAccepted($bid)
    {
        return self::create([
            'company_id' => $bid->company_id,
            'parcel_id' => $bid->parcel_id,
            'company_bid_id' => $bid->id,
            'notification_type' => 'bid_accepted',
            'title' => 'Bid Accepted',
            'message' => "Your bid for parcel {$bid->parcel->tracking_id} has been accepted!",
            'data' => [
                'bid_id' => $bid->id,
                'parcel_id' => $bid->parcel_id,
                'accepted_charge' => $bid->offered_charge
            ]
        ]);
    }

    public static function createForBidRejected($bid)
    {
        return self::create([
            'company_id' => $bid->company_id,
            'parcel_id' => $bid->parcel_id,
            'company_bid_id' => $bid->id,
            'notification_type' => 'bid_rejected',
            'title' => 'Bid Not Selected',
            'message' => "Your bid for parcel {$bid->parcel->tracking_id} was not selected.",
            'data' => [
                'bid_id' => $bid->id,
                'parcel_id' => $bid->parcel_id,
                'offered_charge' => $bid->offered_charge
            ]
        ]);
    }

    public static function getUnreadCountForCompany($companyId)
    {
        return self::where('company_id', $companyId)->unread()->count();
    }
}
