<?php

namespace App\Http\Requests\Bidding;

use Illuminate\Foundation\Http\FormRequest;
use App\Helpers\Bidding\BiddingSettingsHelper;
use App\Models\Backend\Parcel;

class PlaceCompanyBidRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true; // Authorization handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $parcel = $this->getParcel();
        
        return [
            'offered_charge' => [
                'required',
                'numeric',
                'min:1',
                $parcel ? "min:{$parcel->minimum_bid_amount}" : '',
                $parcel ? "max:{$parcel->maximum_bid_amount}" : ''
            ],
            'estimated_pickup_time' => 'nullable|integer|min:15|max:1440', // 15 minutes to 24 hours
            'estimated_delivery_time' => 'nullable|integer|min:30|max:2880', // 30 minutes to 48 hours
            'message' => 'nullable|string|max:500',
            'contact_person' => 'nullable|string|max:100',
            'contact_phone' => 'nullable|string|max:20',
            'contact_email' => 'nullable|email|max:100',
            'service_features' => 'nullable|array',
            'service_features.*' => 'string|max:100',
            'insurance_coverage' => 'nullable|numeric|min:0|max:100000',
            'priority_delivery' => 'nullable|boolean',
            'terms_conditions' => 'nullable|string|max:1000',
            'vehicle_type' => 'nullable|string|max:50',
            'delivery_guarantee' => 'nullable|boolean',
            'tracking_enabled' => 'nullable|boolean',
            'proof_of_delivery' => 'nullable|boolean'
        ];
    }

    /**
     * Get custom validation messages
     */
    public function messages()
    {
        $parcel = $this->getParcel();
        
        return [
            'offered_charge.required' => 'Bid amount is required',
            'offered_charge.min' => $parcel ? 
                "Bid amount must be at least {$parcel->minimum_bid_amount}" : 
                'Bid amount is too low',
            'offered_charge.max' => $parcel ? 
                "Bid amount cannot exceed {$parcel->maximum_bid_amount}" : 
                'Bid amount is too high',
            'estimated_pickup_time.min' => 'Estimated pickup time must be at least 15 minutes',
            'estimated_pickup_time.max' => 'Estimated pickup time cannot exceed 24 hours',
            'estimated_delivery_time.min' => 'Estimated delivery time must be at least 30 minutes',
            'estimated_delivery_time.max' => 'Estimated delivery time cannot exceed 48 hours',
            'contact_email.email' => 'Please provide a valid email address',
            'insurance_coverage.max' => 'Insurance coverage cannot exceed 100,000'
        ];
    }

    /**
     * Configure the validator instance
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $parcel = $this->getParcel();
            
            if (!$parcel) {
                $validator->errors()->add('parcel_id', 'Parcel not found');
                return;
            }

            // Validate bid amount against parcel constraints
            if ($this->has('offered_charge')) {
                $validation = BiddingSettingsHelper::validateBidAmount(
                    $this->offered_charge,
                    $parcel->offered_delivery_charge
                );
                
                if (!$validation['valid']) {
                    $validator->errors()->add('offered_charge', $validation['message']);
                }
            }

            // Check if company has already bid on this parcel
            $company = $this->getCurrentCompany();
            if ($company && $parcel) {
                $existingBid = $parcel->companyBids()
                    ->where('company_id', $company->id)
                    ->where('status', 'pending')
                    ->exists();
                    
                if ($existingBid) {
                    $bidLimit = BiddingSettingsHelper::getCompanyBidLimit();
                    if ($bidLimit <= 1) {
                        $validator->errors()->add('company_id', 
                            'Your company has already placed a bid on this parcel');
                    }
                }
            }

            // Validate delivery time is reasonable
            if ($this->has('estimated_pickup_time') && $this->has('estimated_delivery_time')) {
                if ($this->estimated_delivery_time <= $this->estimated_pickup_time) {
                    $validator->errors()->add('estimated_delivery_time', 
                        'Delivery time must be greater than pickup time');
                }
            }

            // Validate insurance coverage if required
            if ($parcel && $parcel->insurance_required && !$this->has('insurance_coverage')) {
                $validator->errors()->add('insurance_coverage', 
                    'Insurance coverage is required for this parcel');
            }
        });
    }

    /**
     * Prepare the data for validation
     */
    protected function prepareForValidation()
    {
        $company = $this->getCurrentCompany();
        
        // Set default values from company settings
        if ($company) {
            if (!$this->has('estimated_pickup_time')) {
                $this->merge(['estimated_pickup_time' => $company->default_pickup_time ?? 60]);
            }
            
            if (!$this->has('contact_person')) {
                $this->merge(['contact_person' => $company->name]);
            }
            
            if (!$this->has('contact_phone')) {
                $this->merge(['contact_phone' => $company->phone]);
            }
            
            if (!$this->has('contact_email')) {
                $this->merge(['contact_email' => $company->email]);
            }
        }

        // Set default service features
        if (!$this->has('service_features')) {
            $this->merge(['service_features' => ['Standard Delivery', 'SMS Notifications']]);
        }
    }

    /**
     * Get the parcel being bid on
     */
    private function getParcel()
    {
        $parcelId = $this->route('id') ?? $this->route('parcelId') ?? $this->input('parcel_id');
        
        if (!$parcelId) {
            return null;
        }
        
        return Parcel::find($parcelId);
    }

    /**
     * Get current company
     */
    private function getCurrentCompany()
    {
        // This should match the logic in your middleware
        return request()->get('current_company') ?? auth()->user()->company ?? null;
    }
}
