<?php

namespace App\Models\Backend;

use App\Enums\Status;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class DeliveryMan extends Model
{
    use HasFactory, LogsActivity;

    protected $table = 'delivery_man';
    protected $fillable = [
        'company_id',
        'user_id',
        'status',
        'delivery_charge',
        'pickup_charge',
        'return_charge',
        'opening_balance',
        'current_balance',
        // Bidding fields
        'currently_available',
        'current_lat',
        'current_lng',
        'last_location_update',
        'bid_parcel_count',
        'bid_parcel_earnings',
        'average_rating',
        'completed_deliveries'
    ];


    public function scopeOrderByDesc($query, $data)
    {
        $query->orderBy($data, 'desc');
    }
    /**
     * Activity Log
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->useLogName('DeliveryMan')
            ->logOnly(['user.name', 'current_balance',])
            ->setDescriptionForEvent(fn(string $eventName) => "{$eventName}");
    }

    // Get active row this model.
    public function scopeActive($query)
    {
        $query->where('status', Status::ACTIVE);
    }

    public function getMyStatusAttribute()
    {
        if ($this->status == Status::ACTIVE) {
            $status = '<span class="badge badge-pill badge-success">' . trans("status." . $this->status) . '</span>';
        } else {
            $status = '<span class="badge badge-pill badge-danger">' . trans("status." . $this->status) . '</span>';
        }
        return $status;
    }

    public function getDrivingLicenseImageAttribute()
    {
        if (!empty($this->uploadLicense->original['original']) && file_exists(public_path($this->uploadLicense->original['original']))) {
            return static_asset($this->uploadLicense->original['original']);
        }
        return static_asset('images/default/user.png');
    }

    public function uploadLicense()
    {
        return $this->belongsTo(Upload::class, 'driving_license_image_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function hub()
    {
        return $this->belongsTo(Hub::class, 'hub_id', 'id');
    }


    public function scopeCompanywise($query)
    {
        return $query->where('company_id', settings()->id);
    }

    // Bidding relationships
    public function assignedBidParcels()
    {
        return $this->hasMany(Parcel::class, 'assigned_deliveryman_id', 'id')
            ->where('is_bid_parcel', true);
    }

    public function company()
    {
        return $this->belongsTo(GeneralSettings::class, 'company_id', 'id');
    }

    // Bidding scopes
    public function scopeAvailable($query)
    {
        return $query->where('currently_available', true)
            ->where('status', Status::ACTIVE);
    }

    public function scopeInArea($query, $lat, $lng, $radius = 10)
    {
        return $query->whereRaw(
            "ST_Distance_Sphere(
                POINT(current_lng, current_lat),
                POINT(?, ?)
            ) <= ?",
            [$lng, $lat, $radius * 1000] // Convert km to meters
        );
    }

    // Bidding accessors
    public function getCurrentlyAvailableAttribute($value)
    {
        return (bool) $value;
    }

    public function getDistanceFromAttribute()
    {
        return function ($lat, $lng) {
            if (!$this->current_lat || !$this->current_lng) {
                return null;
            }

            $earthRadius = 6371; // Earth's radius in kilometers

            $latDiff = deg2rad($lat - $this->current_lat);
            $lngDiff = deg2rad($lng - $this->current_lng);

            $a = sin($latDiff / 2) * sin($latDiff / 2) +
                cos(deg2rad($this->current_lat)) * cos(deg2rad($lat)) *
                sin($lngDiff / 2) * sin($lngDiff / 2);

            $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

            return round($earthRadius * $c, 2);
        };
    }

    // Bidding business logic methods
    public function canBeAssignedToBidParcel()
    {
        return $this->status === Status::ACTIVE &&
            $this->currently_available &&
            $this->company &&
            $this->company->bidding_enabled;
    }

    public function updateLocation($lat, $lng)
    {
        $this->update([
            'current_lat' => $lat,
            'current_lng' => $lng,
            'last_location_update' => now()
        ]);
    }

    public function markAvailable()
    {
        $this->update(['currently_available' => true]);
    }

    public function markUnavailable()
    {
        $this->update(['currently_available' => false]);
    }

    public function getBidParcelPerformance()
    {
        return [
            'total_assigned' => $this->assignedBidParcels()->count(),
            'completed' => $this->assignedBidParcels()
                ->whereIn('status', ['delivered', 'partial_delivered'])
                ->count(),
            'total_earnings' => $this->bid_parcel_earnings ?? 0,
            'average_rating' => $this->average_rating ?? 0,
            'completion_rate' => $this->assignedBidParcels()->count() > 0 ?
                round(($this->assignedBidParcels()
                    ->whereIn('status', ['delivered', 'partial_delivered'])
                    ->count() / $this->assignedBidParcels()->count()) * 100, 2) : 0
        ];
    }

}
