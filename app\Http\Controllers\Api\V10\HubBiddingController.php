<?php

namespace App\Http\Controllers\Api\V10;

use App\Http\Controllers\Controller;
use App\Models\Backend\Hub;
use App\Models\Backend\GeneralSettings;
use App\Models\Backend\Parcel;
use App\Models\Backend\CompanyBid;
use App\Services\HubBiddingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class HubBiddingController extends Controller
{
    protected $hubBiddingService;

    public function __construct(HubBiddingService $hubBiddingService)
    {
        $this->hubBiddingService = $hubBiddingService;
    }

    /**
     * Get companies in a hub
     */
    public function getHubCompanies(Request $request, $hubId)
    {
        try {
            $hub = Hub::find($hubId);
            if (!$hub) {
                return response()->json([
                    'success' => false,
                    'message' => 'Hub not found'
                ], 404);
            }

            $companies = GeneralSettings::where('hub_id', $hubId)
                ->with(['companyBids' => function($query) {
                    $query->where('created_at', '>=', Carbon::now()->subDays(30));
                }])
                ->get();

            $companiesData = $companies->map(function($company) {
                $metrics = [
                    'total_bids' => $company->companyBids->count(),
                    'won_bids' => $company->companyBids->where('status', 'accepted')->count(),
                    'pending_bids' => $company->companyBids->where('status', 'pending')->count(),
                    'success_rate' => $company->getBiddingSuccessRate(),
                    'available_delivery_men' => $company->getAvailableDeliveryMenCount()
                ];

                return [
                    'id' => $company->id,
                    'name' => $company->name,
                    'phone' => $company->phone,
                    'bidding_enabled' => $company->bidding_enabled,
                    'auto_bid_enabled' => $company->auto_bid_enabled,
                    'min_bid_amount' => $company->min_bid_amount,
                    'max_bid_amount' => $company->max_bid_amount,
                    'default_pickup_time' => $company->default_pickup_time,
                    'metrics' => $metrics,
                    'created_at' => $company->created_at
                ];
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'hub' => [
                        'id' => $hub->id,
                        'name' => $hub->name,
                        'address' => $hub->address
                    ],
                    'companies' => $companiesData,
                    'total_companies' => $companiesData->count(),
                    'bidding_enabled_companies' => $companiesData->where('bidding_enabled', true)->count()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get hub companies: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get bidding statistics for a hub
     */
    public function getBiddingStatistics(Request $request, $hubId)
    {
        try {
            $days = $request->get('days', 30);
            $startDate = Carbon::now()->subDays($days);

            $statistics = $this->hubBiddingService->getHubBiddingStatistics($hubId);
            
            if (!$statistics) {
                return response()->json([
                    'success' => false,
                    'message' => 'Hub not found'
                ], 404);
            }

            // Additional time-based statistics
            $timeBasedStats = [
                'period_days' => $days,
                'bid_parcels_created' => Parcel::where('hub_id', $hubId)
                    ->where('is_bid_parcel', true)
                    ->where('created_at', '>=', $startDate)
                    ->count(),
                'bids_placed' => CompanyBid::where('hub_id', $hubId)
                    ->where('created_at', '>=', $startDate)
                    ->count(),
                'bids_won' => CompanyBid::where('hub_id', $hubId)
                    ->where('status', 'accepted')
                    ->where('created_at', '>=', $startDate)
                    ->count(),
                'average_bid_amount' => CompanyBid::where('hub_id', $hubId)
                    ->where('created_at', '>=', $startDate)
                    ->avg('offered_charge'),
                'total_bid_value' => CompanyBid::where('hub_id', $hubId)
                    ->where('status', 'accepted')
                    ->where('created_at', '>=', $startDate)
                    ->sum('offered_charge')
            ];

            // Daily statistics for charts
            $dailyStats = [];
            for ($i = $days - 1; $i >= 0; $i--) {
                $date = Carbon::now()->subDays($i)->format('Y-m-d');
                $dayStart = Carbon::now()->subDays($i)->startOfDay();
                $dayEnd = Carbon::now()->subDays($i)->endOfDay();

                $dailyStats[] = [
                    'date' => $date,
                    'parcels_created' => Parcel::where('hub_id', $hubId)
                        ->where('is_bid_parcel', true)
                        ->whereBetween('created_at', [$dayStart, $dayEnd])
                        ->count(),
                    'bids_placed' => CompanyBid::where('hub_id', $hubId)
                        ->whereBetween('created_at', [$dayStart, $dayEnd])
                        ->count(),
                    'bids_won' => CompanyBid::where('hub_id', $hubId)
                        ->where('status', 'accepted')
                        ->whereBetween('created_at', [$dayStart, $dayEnd])
                        ->count()
                ];
            }

            // Company performance ranking
            $companyRanking = GeneralSettings::where('hub_id', $hubId)
                ->where('bidding_enabled', true)
                ->get()
                ->map(function($company) use ($startDate) {
                    $bids = $company->companyBids()->where('created_at', '>=', $startDate);
                    $totalBids = $bids->count();
                    $wonBids = $bids->where('status', 'accepted')->count();
                    
                    return [
                        'company_id' => $company->id,
                        'company_name' => $company->name,
                        'total_bids' => $totalBids,
                        'won_bids' => $wonBids,
                        'success_rate' => $totalBids > 0 ? round(($wonBids / $totalBids) * 100, 2) : 0,
                        'total_value' => $bids->where('status', 'accepted')->sum('offered_charge')
                    ];
                })
                ->sortByDesc('success_rate')
                ->values();

            return response()->json([
                'success' => true,
                'data' => [
                    'hub_overview' => $statistics,
                    'time_based_stats' => $timeBasedStats,
                    'daily_stats' => $dailyStats,
                    'company_ranking' => $companyRanking
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get bidding statistics: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle bidding for a company
     */
    public function toggleCompanyBidding(Request $request, $hubId, $companyId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'bidding_enabled' => 'required|boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $company = GeneralSettings::where('id', $companyId)
                                    ->where('hub_id', $hubId)
                                    ->first();

            if (!$company) {
                return response()->json([
                    'success' => false,
                    'message' => 'Company not found in this hub'
                ], 404);
            }

            $company->update([
                'bidding_enabled' => $request->bidding_enabled
            ]);

            $action = $request->bidding_enabled ? 'enabled' : 'disabled';

            return response()->json([
                'success' => true,
                'message' => "Bidding {$action} for company {$company->name}",
                'data' => [
                    'company_id' => $company->id,
                    'company_name' => $company->name,
                    'bidding_enabled' => $company->bidding_enabled
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to toggle company bidding: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get active bid parcels in hub
     */
    public function getActiveBidParcels(Request $request, $hubId)
    {
        try {
            $parcels = Parcel::with(['merchant', 'companyBids.company'])
                ->where('hub_id', $hubId)
                ->where('is_bid_parcel', true)
                ->where('bid_status', 'open')
                ->orderBy('created_at', 'desc')
                ->paginate(20);

            $parcelsData = $parcels->getCollection()->map(function($parcel) {
                return [
                    'id' => $parcel->id,
                    'tracking_id' => $parcel->tracking_id,
                    'merchant_name' => $parcel->merchant->business_name ?? 'N/A',
                    'pickup_address' => $parcel->pickup_address,
                    'customer_address' => $parcel->customer_address,
                    'offered_delivery_charge' => $parcel->offered_delivery_charge,
                    'active_bids_count' => $parcel->active_bids_count,
                    'lowest_bid' => $parcel->lowest_bid?->offered_charge,
                    'highest_bid' => $parcel->highest_bid?->offered_charge,
                    'bid_timeout_at' => $parcel->bid_timeout_at,
                    'time_remaining_hours' => $parcel->bid_timeout_at ? 
                        Carbon::now()->diffInHours($parcel->bid_timeout_at, false) : null,
                    'created_at' => $parcel->created_at
                ];
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'parcels' => $parcelsData,
                    'pagination' => [
                        'current_page' => $parcels->currentPage(),
                        'last_page' => $parcels->lastPage(),
                        'per_page' => $parcels->perPage(),
                        'total' => $parcels->total()
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get active bid parcels: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update hub bidding settings
     */
    public function updateHubBiddingSettings(Request $request, $hubId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'bid_timeout_hours' => 'nullable|integer|min:1|max:168', // 1 hour to 1 week
                'min_companies_required' => 'nullable|integer|min:1|max:10',
                'auto_accept_enabled' => 'nullable|boolean',
                'notification_enabled' => 'nullable|boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $hub = Hub::find($hubId);
            if (!$hub) {
                return response()->json([
                    'success' => false,
                    'message' => 'Hub not found'
                ], 404);
            }

            // Update hub-specific bidding settings
            // This would require adding bidding settings fields to the hubs table
            // For now, we'll return success with the settings that would be updated

            return response()->json([
                'success' => true,
                'message' => 'Hub bidding settings updated successfully',
                'data' => [
                    'hub_id' => $hubId,
                    'settings' => $request->only([
                        'bid_timeout_hours',
                        'min_companies_required', 
                        'auto_accept_enabled',
                        'notification_enabled'
                    ])
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update hub bidding settings: ' . $e->getMessage()
            ], 500);
        }
    }
}
