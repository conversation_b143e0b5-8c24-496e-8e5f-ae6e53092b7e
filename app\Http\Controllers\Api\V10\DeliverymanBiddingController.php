<?php

namespace App\Http\Controllers\Api\V10;

use App\Http\Controllers\Controller;
use App\Models\Backend\Parcel;
use App\Models\Backend\DeliveryMan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class DeliverymanBiddingController extends Controller
{
    /**
     * Get assigned bid parcels for deliveryman
     */
    public function getAssignedBidParcels(Request $request)
    {
        try {
            $deliveryman = $this->getCurrentDeliveryman();
            if (!$deliveryman) {
                return response()->json([
                    'success' => false,
                    'message' => 'Deliveryman not found'
                ], 404);
            }

            $status = $request->get('status', 'all');
            $query = Parcel::with(['merchant', 'winningCompany'])
                ->where('assigned_deliveryman_id', $deliveryman->id)
                ->where('is_bid_parcel', true);

            if ($status !== 'all') {
                $query->where('status', $status);
            }

            $parcels = $query->orderBy('created_at', 'desc')->paginate(20);

            $parcelsData = $parcels->getCollection()->map(function($parcel) {
                return [
                    'id' => $parcel->id,
                    'tracking_id' => $parcel->tracking_id,
                    'merchant' => [
                        'name' => $parcel->merchant->business_name ?? 'N/A',
                        'phone' => $parcel->merchant->phone ?? 'N/A'
                    ],
                    'company' => [
                        'name' => $parcel->winningCompany->name ?? 'N/A',
                        'phone' => $parcel->winningCompany->phone ?? 'N/A'
                    ],
                    'pickup_address' => $parcel->pickup_address,
                    'pickup_phone' => $parcel->pickup_phone,
                    'customer_name' => $parcel->customer_name,
                    'customer_phone' => $parcel->customer_phone,
                    'customer_address' => $parcel->customer_address,
                    'weight' => $parcel->weight,
                    'accepted_delivery_charge' => $parcel->accepted_delivery_charge,
                    'cash_collection' => $parcel->cash_collection,
                    'cod_amount' => $parcel->cod_amount,
                    'status' => $parcel->status,
                    'bid_status' => $parcel->bid_status,
                    'pickup_date' => $parcel->pickup_date,
                    'delivery_date' => $parcel->delivery_date,
                    'created_at' => $parcel->created_at,
                    'note' => $parcel->note
                ];
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'parcels' => $parcelsData,
                    'pagination' => [
                        'current_page' => $parcels->currentPage(),
                        'last_page' => $parcels->lastPage(),
                        'per_page' => $parcels->perPage(),
                        'total' => $parcels->total()
                    ],
                    'statistics' => [
                        'total_assigned' => Parcel::where('assigned_deliveryman_id', $deliveryman->id)
                                                 ->where('is_bid_parcel', true)->count(),
                        'pending' => Parcel::where('assigned_deliveryman_id', $deliveryman->id)
                                          ->where('is_bid_parcel', true)
                                          ->where('status', 'pending')->count(),
                        'in_progress' => Parcel::where('assigned_deliveryman_id', $deliveryman->id)
                                              ->where('is_bid_parcel', true)
                                              ->whereIn('status', ['pickup_assigned', 'pickup_in_progress', 'in_transit'])->count(),
                        'delivered' => Parcel::where('assigned_deliveryman_id', $deliveryman->id)
                                            ->where('is_bid_parcel', true)
                                            ->where('status', 'delivered')->count()
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get assigned bid parcels: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update parcel status
     */
    public function updateParcelStatus(Request $request, $parcelId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'status' => 'required|string|in:pickup_assigned,pickup_in_progress,in_transit,delivered,cancelled',
                'note' => 'nullable|string|max:500',
                'delivery_proof' => 'nullable|string', // Base64 image or file path
                'customer_signature' => 'nullable|string', // Base64 signature
                'delivery_lat' => 'nullable|numeric',
                'delivery_lng' => 'nullable|numeric'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $deliveryman = $this->getCurrentDeliveryman();
            $parcel = Parcel::find($parcelId);

            if (!$parcel) {
                return response()->json([
                    'success' => false,
                    'message' => 'Parcel not found'
                ], 404);
            }

            if ($parcel->assigned_deliveryman_id !== $deliveryman->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access to parcel'
                ], 403);
            }

            // Update parcel status
            $updateData = [
                'status' => $request->status
            ];

            if ($request->note) {
                $updateData['note'] = $request->note;
            }

            if ($request->status === 'delivered') {
                $updateData['delivery_date'] = Carbon::now();
                
                if ($request->delivery_lat && $request->delivery_lng) {
                    $updateData['customer_lat'] = $request->delivery_lat;
                    $updateData['customer_long'] = $request->delivery_lng;
                }
            }

            $parcel->update($updateData);

            // Log the status change
            $this->logParcelStatusChange($parcel, $request->status, $deliveryman->id, $request->note);

            return response()->json([
                'success' => true,
                'message' => 'Parcel status updated successfully',
                'data' => [
                    'parcel' => $parcel->fresh(),
                    'new_status' => $request->status
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update parcel status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update deliveryman location
     */
    public function updateLocation(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'latitude' => 'required|numeric|between:-90,90',
                'longitude' => 'required|numeric|between:-180,180'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $deliveryman = $this->getCurrentDeliveryman();
            if (!$deliveryman) {
                return response()->json([
                    'success' => false,
                    'message' => 'Deliveryman not found'
                ], 404);
            }

            // Update deliveryman location
            $deliveryman->update([
                'current_lat' => $request->latitude,
                'current_lng' => $request->longitude,
                'last_location_update' => Carbon::now()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Location updated successfully',
                'data' => [
                    'latitude' => $deliveryman->current_lat,
                    'longitude' => $deliveryman->current_lng,
                    'updated_at' => $deliveryman->last_location_update
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update location: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get deliveryman performance metrics for bid parcels
     */
    public function getBidPerformanceMetrics(Request $request)
    {
        try {
            $deliveryman = $this->getCurrentDeliveryman();
            if (!$deliveryman) {
                return response()->json([
                    'success' => false,
                    'message' => 'Deliveryman not found'
                ], 404);
            }

            $days = $request->get('days', 30);
            $startDate = Carbon::now()->subDays($days);

            $totalBidParcels = Parcel::where('assigned_deliveryman_id', $deliveryman->id)
                ->where('is_bid_parcel', true)
                ->where('created_at', '>=', $startDate)
                ->count();

            $deliveredParcels = Parcel::where('assigned_deliveryman_id', $deliveryman->id)
                ->where('is_bid_parcel', true)
                ->where('status', 'delivered')
                ->where('created_at', '>=', $startDate)
                ->count();

            $totalEarnings = Parcel::where('assigned_deliveryman_id', $deliveryman->id)
                ->where('is_bid_parcel', true)
                ->where('status', 'delivered')
                ->where('created_at', '>=', $startDate)
                ->sum('accepted_delivery_charge');

            $averageDeliveryTime = $this->calculateAverageDeliveryTime($deliveryman->id, $startDate);

            return response()->json([
                'success' => true,
                'data' => [
                    'period_days' => $days,
                    'total_bid_parcels' => $totalBidParcels,
                    'delivered_parcels' => $deliveredParcels,
                    'delivery_success_rate' => $totalBidParcels > 0 ? 
                        round(($deliveredParcels / $totalBidParcels) * 100, 2) : 0,
                    'total_earnings' => round($totalEarnings, 2),
                    'average_earnings_per_parcel' => $deliveredParcels > 0 ? 
                        round($totalEarnings / $deliveredParcels, 2) : 0,
                    'average_delivery_time_hours' => $averageDeliveryTime,
                    'current_rating' => $deliveryman->average_rating ?? 5.0
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get performance metrics: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Private helper methods
     */
    private function getCurrentDeliveryman()
    {
        // Get the authenticated deliveryman
        // This might need adjustment based on your authentication structure
        return Auth::user()->deliveryman ?? DeliveryMan::first();
    }

    private function logParcelStatusChange($parcel, $status, $deliverymanId, $note = null)
    {
        // Log the status change - this could be implemented with a ParcelLog model
        // For now, we'll just log it
        \Log::info("Parcel {$parcel->tracking_id} status changed to {$status} by deliveryman {$deliverymanId}", [
            'parcel_id' => $parcel->id,
            'old_status' => $parcel->getOriginal('status'),
            'new_status' => $status,
            'deliveryman_id' => $deliverymanId,
            'note' => $note,
            'timestamp' => Carbon::now()
        ]);
    }

    private function calculateAverageDeliveryTime($deliverymanId, $startDate)
    {
        // Calculate average delivery time for delivered parcels
        // This is a simplified calculation - in reality you'd track pickup and delivery timestamps
        $deliveredParcels = Parcel::where('assigned_deliveryman_id', $deliverymanId)
            ->where('is_bid_parcel', true)
            ->where('status', 'delivered')
            ->where('created_at', '>=', $startDate)
            ->get();

        if ($deliveredParcels->isEmpty()) {
            return 0;
        }

        $totalHours = 0;
        foreach ($deliveredParcels as $parcel) {
            // Simplified: assume delivery time is from created_at to delivery_date
            if ($parcel->delivery_date) {
                $hours = Carbon::parse($parcel->created_at)->diffInHours(Carbon::parse($parcel->delivery_date));
                $totalHours += $hours;
            }
        }

        return $deliveredParcels->count() > 0 ? round($totalHours / $deliveredParcels->count(), 2) : 0;
    }
}
