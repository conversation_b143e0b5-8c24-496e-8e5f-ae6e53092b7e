<?php

namespace App\Http\Controllers\Api\V10;

use App\Http\Controllers\Controller;
use App\Helpers\Bidding\BiddingSettingsHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class BiddingSettingsController extends Controller
{
    /**
     * Get all bidding settings
     */
    public function getBiddingSettings(Request $request)
    {
        try {
            $category = $request->get('category', 'all');
            
            switch ($category) {
                case 'core':
                    $settings = [
                        'bidding_enabled' => BiddingSettingsHelper::isBiddingEnabled(),
                        'bid_timeout_hours' => BiddingSettingsHelper::getBidTimeoutHours(),
                        'min_companies_per_hub' => BiddingSettingsHelper::getMinCompaniesPerHub(),
                        'auto_accept_best_bid' => BiddingSettingsHelper::isAutoAcceptEnabled(),
                        'company_bid_limit' => BiddingSettingsHelper::getCompanyBidLimit(),
                        'hub_commission' => BiddingSettingsHelper::getHubCommission()
                    ];
                    break;
                    
                case 'constraints':
                    $settings = array_merge(
                        BiddingSettingsHelper::getBiddingPercentageConstraints(),
                        BiddingSettingsHelper::getDefaultBidConstraints()
                    );
                    break;
                    
                case 'notifications':
                    $settings = BiddingSettingsHelper::getNotificationSettings();
                    break;
                    
                case 'company_defaults':
                    $settings = BiddingSettingsHelper::getCompanyDefaults();
                    break;
                    
                case 'performance':
                    $settings = BiddingSettingsHelper::getPerformanceSettings();
                    break;
                    
                case 'security':
                    $settings = BiddingSettingsHelper::getSecuritySettings();
                    break;
                    
                case 'features':
                    $settings = [
                        'suggestions_enabled' => BiddingSettingsHelper::isFeatureEnabled('suggestions'),
                        'auto_bidding_enabled' => BiddingSettingsHelper::isFeatureEnabled('auto_bidding'),
                        'analytics_enabled' => BiddingSettingsHelper::isFeatureEnabled('analytics'),
                        'priority_enabled' => BiddingSettingsHelper::isFeatureEnabled('priority')
                    ];
                    break;
                    
                default:
                    $settings = BiddingSettingsHelper::getAll();
                    break;
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'category' => $category,
                    'settings' => $settings
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get bidding settings: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update bidding settings
     */
    public function updateBiddingSettings(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'settings' => 'required|array',
                'settings.*.key' => 'required|string',
                'settings.*.value' => 'required',
                'settings.*.type' => 'nullable|string|in:string,integer,decimal,boolean,array'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $updatedSettings = [];
            foreach ($request->settings as $setting) {
                $key = $setting['key'];
                $value = $setting['value'];
                $type = $setting['type'] ?? 'string';

                // Validate specific settings
                $validation = $this->validateSetting($key, $value);
                if (!$validation['valid']) {
                    return response()->json([
                        'success' => false,
                        'message' => "Invalid value for {$key}: {$validation['message']}"
                    ], 400);
                }

                BiddingSettingsHelper::set($key, $value, $type);
                $updatedSettings[$key] = $value;
            }

            return response()->json([
                'success' => true,
                'message' => 'Bidding settings updated successfully',
                'data' => [
                    'updated_settings' => $updatedSettings
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update bidding settings: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reset bidding settings to defaults
     */
    public function resetBiddingSettings(Request $request)
    {
        try {
            BiddingSettingsHelper::resetToDefaults();

            return response()->json([
                'success' => true,
                'message' => 'Bidding settings reset to defaults successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to reset bidding settings: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear bidding settings cache
     */
    public function clearSettingsCache(Request $request)
    {
        try {
            BiddingSettingsHelper::clearCache();

            return response()->json([
                'success' => true,
                'message' => 'Bidding settings cache cleared successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear settings cache: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate bid amount
     */
    public function validateBidAmount(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'bid_amount' => 'required|numeric|min:0.01',
                'offered_amount' => 'required|numeric|min:0.01'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $validation = BiddingSettingsHelper::validateBidAmount(
                $request->bid_amount,
                $request->offered_amount
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'valid' => $validation['valid'],
                    'message' => $validation['message'],
                    'bid_amount' => $request->bid_amount,
                    'offered_amount' => $request->offered_amount
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to validate bid amount: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get bidding system status
     */
    public function getBiddingStatus(Request $request)
    {
        try {
            $status = [
                'system_enabled' => BiddingSettingsHelper::isBiddingEnabled(),
                'notifications_enabled' => BiddingSettingsHelper::areBidNotificationsEnabled(),
                'auto_accept_enabled' => BiddingSettingsHelper::isAutoAcceptEnabled(),
                'features' => [
                    'suggestions' => BiddingSettingsHelper::isFeatureEnabled('suggestions'),
                    'auto_bidding' => BiddingSettingsHelper::isFeatureEnabled('auto_bidding'),
                    'analytics' => BiddingSettingsHelper::isFeatureEnabled('analytics'),
                    'priority' => BiddingSettingsHelper::isFeatureEnabled('priority')
                ],
                'constraints' => BiddingSettingsHelper::getBiddingPercentageConstraints(),
                'timeouts' => [
                    'bid_timeout_hours' => BiddingSettingsHelper::getBidTimeoutHours(),
                    'reminder_hours' => BiddingSettingsHelper::get('bid_deadline_reminder_hours', 2)
                ]
            ];

            return response()->json([
                'success' => true,
                'data' => $status
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get bidding status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate individual setting
     */
    private function validateSetting($key, $value)
    {
        switch ($key) {
            case 'bid_timeout_hours':
                if (!is_numeric($value) || $value < 1 || $value > 168) {
                    return ['valid' => false, 'message' => 'Timeout must be between 1 and 168 hours'];
                }
                break;
                
            case 'bidding_min_percentage':
                if (!is_numeric($value) || $value < 1 || $value > 100) {
                    return ['valid' => false, 'message' => 'Minimum percentage must be between 1 and 100'];
                }
                break;
                
            case 'bidding_max_percentage':
                if (!is_numeric($value) || $value < 100 || $value > 1000) {
                    return ['valid' => false, 'message' => 'Maximum percentage must be between 100 and 1000'];
                }
                break;
                
            case 'hub_bidding_commission':
                if (!is_numeric($value) || $value < 0 || $value > 50) {
                    return ['valid' => false, 'message' => 'Commission must be between 0 and 50 percent'];
                }
                break;
                
            case 'min_companies_per_hub':
                if (!is_numeric($value) || $value < 1 || $value > 20) {
                    return ['valid' => false, 'message' => 'Minimum companies must be between 1 and 20'];
                }
                break;
                
            case 'company_bid_limit_per_parcel':
                if (!is_numeric($value) || $value < 1 || $value > 10) {
                    return ['valid' => false, 'message' => 'Bid limit must be between 1 and 10'];
                }
                break;
        }

        return ['valid' => true, 'message' => 'Valid'];
    }
}
