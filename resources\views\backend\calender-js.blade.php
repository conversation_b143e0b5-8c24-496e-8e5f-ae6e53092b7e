
<script type="text/javascript" src="{{ static_asset('backend/vendor/calender/main.js') }}"></script>
<script>

  document.addEventListener('DOMContentLoaded', function() {
    var calendarEl = document.getElementById('calendar');
    if(calendarEl !=null){
    var calendar = new FullCalendar.Calendar(calendarEl, {
        initialDate: '{{ date("Y-m-d") }}',
        editable: true,
        selectable: true,
        businessHours: true,
        dayMaxEvents: true, // allow "more" link when too many events
        events: [
                @php
                    $newsOffer = null;
                    try {
                        if(function_exists('calendarnewsoffer')) {
                            $newsOffer = calendarnewsoffer(date("Y-m-d"));
                        }
                    } catch (Exception $e) {
                        $newsOffer = null;
                    }
                @endphp
                @if($newsOffer !== null)
            {
                title: 'News & Offer : {{ addslashes($newsOffer->title ?? 'No Title') }}',
                start: '{{ date("Y-m-d") }}'
            }
                @endif
        ]
    });
    calendar.render();
}
  });

</script>
