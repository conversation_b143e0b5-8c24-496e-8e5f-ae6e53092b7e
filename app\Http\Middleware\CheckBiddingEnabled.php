<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Helpers\Bidding\BiddingSettingsHelper;

class CheckBiddingEnabled
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Check if bidding system is enabled globally
        if (!BiddingSettingsHelper::isBiddingEnabled()) {
            return response()->json([
                'success' => false,
                'message' => 'Bidding system is currently disabled',
                'error_code' => 'BIDDING_DISABLED'
            ], 403);
        }

        return $next($request);
    }
}
