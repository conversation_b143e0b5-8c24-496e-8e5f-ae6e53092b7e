<?php

namespace App\Services;

use App\Models\Backend\Parcel;
use App\Models\Backend\CompanyBid;
use App\Models\Backend\BidNotification;
use App\Models\Backend\GeneralSettings;
use App\Models\Backend\Hub;
use App\Helpers\Bidding\BiddingNotificationHelper;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class HubBiddingService
{
    protected $notificationHelper;

    public function __construct(BiddingNotificationHelper $notificationHelper)
    {
        $this->notificationHelper = $notificationHelper;
    }

    /**
     * Create a bid parcel and notify all companies in the hub
     */
    public function createBidParcel($merchantId, $parcelData)
    {
        try {
            DB::beginTransaction();

            // Get merchant's hub
            $merchant = \App\Models\Backend\Merchant::find($merchantId);
            if (!$merchant || !$merchant->user->hub_id) {
                throw new \Exception('Merchant hub not found');
            }

            $hubId = $merchant->user->hub_id;

            // Create the parcel with bidding fields
            $parcelData = array_merge($parcelData, [
                'is_bid_parcel' => true,
                'bid_status' => 'open',
                'bid_timeout_at' => $this->calculateBidTimeout($parcelData),
                'hub_id' => $hubId
            ]);

            $parcel = Parcel::create($parcelData);

            // Get all companies in the same hub that can bid
            $companies = $this->getEligibleCompanies($hubId, $parcel);

            if ($companies->isEmpty()) {
                throw new \Exception('No eligible companies found in hub');
            }

            // Notify all companies about the new bid opportunity
            $this->notifyCompaniesOfNewBid($parcel, $companies);

            DB::commit();

            return [
                'success' => true,
                'parcel' => $parcel,
                'notified_companies' => $companies->count(),
                'message' => 'Bid parcel created and companies notified successfully'
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create bid parcel: ' . $e->getMessage());
            
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Process a company bid on a parcel
     */
    public function processCompanyBid($companyId, $parcelId, $bidData)
    {
        try {
            DB::beginTransaction();

            $parcel = Parcel::find($parcelId);
            $company = GeneralSettings::find($companyId);

            if (!$parcel || !$company) {
                throw new \Exception('Parcel or company not found');
            }

            // Validate if company can bid on this parcel
            if (!$company->canBidOnParcel($parcel)) {
                throw new \Exception('Company cannot bid on this parcel');
            }

            // Validate bid amount
            if (!$this->validateBidAmount($parcel, $bidData['offered_charge'])) {
                throw new \Exception('Bid amount is outside acceptable range');
            }

            // Create the bid
            $bid = CompanyBid::create(array_merge($bidData, [
                'company_id' => $companyId,
                'parcel_id' => $parcelId,
                'hub_id' => $parcel->hub_id,
                'status' => 'pending',
                'bid_expires_at' => $parcel->bid_timeout_at
            ]));

            // Notify merchant about new bid
            $this->notificationHelper->notifyMerchantOfNewBid($parcel, $bid);

            DB::commit();

            return [
                'success' => true,
                'bid' => $bid,
                'message' => 'Bid placed successfully'
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to process company bid: ' . $e->getMessage());
            
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Accept a company bid
     */
    public function acceptCompanyBid($parcelId, $bidId, $merchantId)
    {
        try {
            DB::beginTransaction();

            $parcel = Parcel::find($parcelId);
            $bid = CompanyBid::find($bidId);

            if (!$parcel || !$bid) {
                throw new \Exception('Parcel or bid not found');
            }

            // Verify merchant owns the parcel
            if ($parcel->merchant_id !== $merchantId) {
                throw new \Exception('Unauthorized access to parcel');
            }

            // Accept the bid
            if (!$parcel->acceptBid($bidId)) {
                throw new \Exception('Failed to accept bid');
            }

            // Send notifications
            $this->notificationHelper->notifyBidAccepted($bid);
            $this->notificationHelper->notifyOtherCompaniesOfBidLoss($parcel, $bid);

            DB::commit();

            return [
                'success' => true,
                'parcel' => $parcel->fresh(),
                'winning_bid' => $bid->fresh(),
                'message' => 'Bid accepted successfully'
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to accept company bid: ' . $e->getMessage());
            
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Expire bid parcels that have timed out
     */
    public function expireBidParcels()
    {
        $expiredParcels = Parcel::where('is_bid_parcel', true)
            ->where('bid_status', 'open')
            ->where('bid_timeout_at', '<', Carbon::now())
            ->get();

        foreach ($expiredParcels as $parcel) {
            $parcel->expireBids();
            $this->notificationHelper->notifyBidExpired($parcel);
        }

        // Also mark individual bids as expired
        CompanyBid::markExpiredBids();

        return $expiredParcels->count();
    }

    /**
     * Get companies eligible to bid in a hub
     */
    public function getEligibleCompanies($hubId, $parcel = null)
    {
        $query = GeneralSettings::where('hub_id', $hubId)
            ->where('bidding_enabled', true);

        if ($parcel) {
            // Add additional filtering based on parcel requirements
            if ($parcel->weight && $parcel->weight > 0) {
                $query->where(function($q) use ($parcel) {
                    $q->whereNull('max_parcel_weight')
                      ->orWhere('max_parcel_weight', '>=', $parcel->weight);
                });
            }
        }

        return $query->get();
    }

    /**
     * Get hub bidding statistics
     */
    public function getHubBiddingStatistics($hubId)
    {
        $hub = Hub::find($hubId);
        if (!$hub) {
            return null;
        }

        $totalCompanies = GeneralSettings::where('hub_id', $hubId)->count();
        $biddingEnabledCompanies = GeneralSettings::where('hub_id', $hubId)
            ->where('bidding_enabled', true)->count();

        $totalBidParcels = Parcel::where('hub_id', $hubId)
            ->where('is_bid_parcel', true)->count();

        $activeBidParcels = Parcel::where('hub_id', $hubId)
            ->where('is_bid_parcel', true)
            ->where('bid_status', 'open')->count();

        $totalBids = CompanyBid::where('hub_id', $hubId)->count();
        $averageBidsPerParcel = $totalBidParcels > 0 ? round($totalBids / $totalBidParcels, 2) : 0;

        return [
            'hub' => $hub,
            'total_companies' => $totalCompanies,
            'bidding_enabled_companies' => $biddingEnabledCompanies,
            'total_bid_parcels' => $totalBidParcels,
            'active_bid_parcels' => $activeBidParcels,
            'total_bids' => $totalBids,
            'average_bids_per_parcel' => $averageBidsPerParcel
        ];
    }

    /**
     * Private helper methods
     */
    private function calculateBidTimeout($parcelData)
    {
        $timeoutHours = SettingHelper('bid_timeout_hours', 24);
        return Carbon::now()->addHours($timeoutHours);
    }

    private function validateBidAmount($parcel, $bidAmount)
    {
        if ($parcel->minimum_bid_amount && $bidAmount < $parcel->minimum_bid_amount) {
            return false;
        }

        if ($parcel->maximum_bid_amount && $bidAmount > $parcel->maximum_bid_amount) {
            return false;
        }

        return true;
    }

    private function notifyCompaniesOfNewBid($parcel, $companies)
    {
        // Create notifications for all companies
        BidNotification::createForNewBid($parcel, $companies);

        // Send real-time notifications
        foreach ($companies as $company) {
            $this->notificationHelper->sendNewBidNotification($company, $parcel);
        }
    }
}
