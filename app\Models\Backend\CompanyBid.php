<?php

namespace App\Models\Backend;

use App\Enums\BidStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Carbon\Carbon;

class CompanyBid extends Model
{
    use HasFactory, LogsActivity;

    protected $table = 'company_bids';

    protected $fillable = [
        'company_id',
        'parcel_id',
        'hub_id',
        'offered_charge',
        'estimated_pickup_time',
        'estimated_delivery_time',
        'message',
        'contact_person',
        'contact_phone',
        'contact_email',
        'status',
        'bid_expires_at',
        'accepted_at',
        'rejected_at',
        'service_features',
        'insurance_coverage',
        'priority_delivery',
        'terms_conditions'
    ];

    protected $casts = [
        'service_features' => 'array',
        'priority_delivery' => 'boolean',
        'offered_charge' => 'decimal:2',
        'insurance_coverage' => 'decimal:2',
        'bid_expires_at' => 'datetime',
        'accepted_at' => 'datetime',
        'rejected_at' => 'datetime',
    ];

    protected $dates = [
        'bid_expires_at',
        'accepted_at',
        'rejected_at',
        'created_at',
        'updated_at'
    ];

    /**
     * Activity Log
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->useLogName('CompanyBid')
            ->logOnly([
                'company.name',
                'parcel.tracking_id',
                'offered_charge',
                'status',
                'estimated_pickup_time'
            ])
            ->setDescriptionForEvent(fn(string $eventName) => "{$eventName}");
    }

    /**
     * Relationships
     */
    public function company()
    {
        return $this->belongsTo(GeneralSettings::class, 'company_id', 'id');
    }

    public function parcel()
    {
        return $this->belongsTo(Parcel::class, 'parcel_id', 'id');
    }

    public function hub()
    {
        return $this->belongsTo(Hub::class, 'hub_id', 'id');
    }

    public function notifications()
    {
        return $this->hasMany(BidNotification::class, 'company_bid_id', 'id');
    }

    /**
     * Scopes
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeAccepted($query)
    {
        return $query->where('status', 'accepted');
    }

    public function scopeExpired($query)
    {
        return $query->where('status', 'expired');
    }

    public function scopeActive($query)
    {
        return $query->whereIn('status', ['pending', 'accepted']);
    }

    public function scopeForCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    public function scopeForHub($query, $hubId)
    {
        return $query->where('hub_id', $hubId);
    }

    public function scopeNotExpired($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('bid_expires_at')
              ->orWhere('bid_expires_at', '>', Carbon::now());
        });
    }

    public function scopeCompanywise($query)
    {
        return $query->where('company_id', settings()->id);
    }

    /**
     * Accessors & Mutators
     */
    public function getIsExpiredAttribute()
    {
        if (!$this->bid_expires_at) {
            return false;
        }
        return Carbon::now()->isAfter($this->bid_expires_at);
    }

    public function getTimeRemainingAttribute()
    {
        if (!$this->bid_expires_at || $this->is_expired) {
            return null;
        }
        return Carbon::now()->diffInMinutes($this->bid_expires_at);
    }

    public function getFormattedOfferedChargeAttribute()
    {
        return number_format($this->offered_charge, 2);
    }

    public function getStatusBadgeAttribute()
    {
        $badges = [
            'pending' => '<span class="badge badge-warning">Pending</span>',
            'accepted' => '<span class="badge badge-success">Accepted</span>',
            'rejected' => '<span class="badge badge-danger">Rejected</span>',
            'expired' => '<span class="badge badge-secondary">Expired</span>',
            'withdrawn' => '<span class="badge badge-info">Withdrawn</span>',
        ];

        return $badges[$this->status] ?? '<span class="badge badge-light">Unknown</span>';
    }

    /**
     * Business Logic Methods
     */
    public function canBeAccepted()
    {
        return $this->status === 'pending' && !$this->is_expired;
    }

    public function canBeWithdrawn()
    {
        return $this->status === 'pending' && !$this->is_expired;
    }

    public function accept()
    {
        if (!$this->canBeAccepted()) {
            return false;
        }

        $this->update([
            'status' => 'accepted',
            'accepted_at' => Carbon::now()
        ]);

        // Reject other bids for the same parcel
        self::where('parcel_id', $this->parcel_id)
            ->where('id', '!=', $this->id)
            ->where('status', 'pending')
            ->update([
                'status' => 'rejected',
                'rejected_at' => Carbon::now()
            ]);

        return true;
    }

    public function reject()
    {
        if ($this->status !== 'pending') {
            return false;
        }

        $this->update([
            'status' => 'rejected',
            'rejected_at' => Carbon::now()
        ]);

        return true;
    }

    public function withdraw()
    {
        if (!$this->canBeWithdrawn()) {
            return false;
        }

        $this->update(['status' => 'withdrawn']);
        return true;
    }

    public function markAsExpired()
    {
        if ($this->status === 'pending') {
            $this->update(['status' => 'expired']);
        }
    }

    /**
     * Static Methods
     */
    public static function getExpiredBids()
    {
        return self::where('status', 'pending')
            ->where('bid_expires_at', '<', Carbon::now())
            ->get();
    }

    public static function markExpiredBids()
    {
        return self::where('status', 'pending')
            ->where('bid_expires_at', '<', Carbon::now())
            ->update(['status' => 'expired']);
    }
}
