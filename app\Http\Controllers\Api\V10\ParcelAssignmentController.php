<?php

namespace App\Http\Controllers\Api\V10;

use App\Http\Controllers\Controller;
use App\Services\ParcelAssignmentService;
use App\Models\Backend\Parcel;
use App\Models\Backend\DeliveryMan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;

class ParcelAssignmentController extends Controller
{
    protected $assignmentService;

    public function __construct(ParcelAssignmentService $assignmentService)
    {
        $this->assignmentService = $assignmentService;
    }

    /**
     * Assign a parcel to delivery man
     */
    public function assignParcel(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'parcel_id' => 'required|exists:parcels,id',
                'deliveryman_id' => 'required|exists:delivery_man,id',
                'company_id' => 'nullable|exists:general_settings,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $result = $this->assignmentService->assignParcel(
                $request->parcel_id,
                $request->deliveryman_id,
                $request->company_id
            );

            return response()->json([
                'success' => true,
                'message' => $result['message'],
                'data' => $result
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to assign parcel: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Unassign a parcel
     */
    public function unassignParcel(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'parcel_id' => 'required|exists:parcels,id',
                'reason' => 'nullable|string|max:255'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $result = $this->assignmentService->unassignParcel(
                $request->parcel_id,
                $request->reason
            );

            return response()->json([
                'success' => true,
                'message' => $result['message']
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to unassign parcel: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Auto-assign parcels
     */
    public function autoAssignParcels(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'hub_id' => 'nullable|exists:hubs,id',
                'company_id' => 'nullable|exists:general_settings,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $assignedCount = $this->assignmentService->autoAssignParcels(
                $request->hub_id,
                $request->company_id
            );

            return response()->json([
                'success' => true,
                'message' => "Successfully auto-assigned {$assignedCount} parcels",
                'data' => [
                    'assigned_count' => $assignedCount
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to auto-assign parcels: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get available delivery men for assignment
     */
    public function getAvailableDeliveryMen(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'parcel_id' => 'required|exists:parcels,id',
                'company_id' => 'nullable|exists:general_settings,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $parcel = Parcel::find($request->parcel_id);
            $companyId = $request->company_id;

            $query = DeliveryMan::where('status', 1)
                ->where('currently_available', true)
                ->where('available_for_bidding', true);

            if ($parcel->is_bid_parcel && $parcel->winning_company_id) {
                // For bid parcels, only show delivery men from winning company
                $query->where('company_id', $parcel->winning_company_id);
            } elseif ($companyId) {
                // Filter by specific company
                $query->where('company_id', $companyId);
            } elseif ($parcel->hub_id) {
                // Filter by hub
                $query->whereHas('company', function($q) use ($parcel) {
                    $q->where('hub_id', $parcel->hub_id);
                });
            }

            $deliveryMen = $query->with(['company'])
                ->orderBy('average_rating', 'desc')
                ->orderBy('completed_deliveries_count', 'asc')
                ->get();

            $deliveryMenData = $deliveryMen->map(function($deliveryman) {
                return [
                    'id' => $deliveryman->id,
                    'name' => $deliveryman->name,
                    'phone' => $deliveryman->phone,
                    'email' => $deliveryman->email,
                    'company' => [
                        'id' => $deliveryman->company->id ?? null,
                        'name' => $deliveryman->company->name ?? 'N/A'
                    ],
                    'average_rating' => $deliveryman->average_rating,
                    'completed_deliveries' => $deliveryman->completed_deliveries_count,
                    'total_bid_deliveries' => $deliveryman->total_bid_deliveries,
                    'current_location' => [
                        'lat' => $deliveryman->current_lat,
                        'lng' => $deliveryman->current_lng,
                        'last_update' => $deliveryman->last_location_update
                    ],
                    'vehicle_info' => [
                        'type' => $deliveryman->vehicle_type,
                        'number' => $deliveryman->vehicle_number,
                        'capacity' => $deliveryman->vehicle_capacity
                    ]
                ];
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'parcel' => [
                        'id' => $parcel->id,
                        'tracking_id' => $parcel->tracking_id,
                        'is_bid_parcel' => $parcel->is_bid_parcel,
                        'bid_status' => $parcel->bid_status,
                        'winning_company_id' => $parcel->winning_company_id
                    ],
                    'available_delivery_men' => $deliveryMenData,
                    'total_available' => $deliveryMenData->count()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get available delivery men: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get assignment statistics
     */
    public function getAssignmentStatistics(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'company_id' => 'nullable|exists:general_settings,id',
                'days' => 'nullable|integer|min:1|max:365'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $statistics = $this->assignmentService->getAssignmentStatistics(
                $request->company_id,
                $request->days ?? 30
            );

            return response()->json([
                'success' => true,
                'data' => $statistics
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get assignment statistics: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get parcel assignment history
     */
    public function getAssignmentHistory(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'parcel_id' => 'nullable|exists:parcels,id',
                'deliveryman_id' => 'nullable|exists:delivery_man,id',
                'company_id' => 'nullable|exists:general_settings,id',
                'days' => 'nullable|integer|min:1|max:365'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $days = $request->days ?? 30;
            $startDate = now()->subDays($days);

            $query = Parcel::with(['assignedDeliveryman.company', 'merchant', 'winningCompany'])
                ->where('created_at', '>=', $startDate)
                ->whereNotNull('assigned_deliveryman_id');

            if ($request->parcel_id) {
                $query->where('id', $request->parcel_id);
            }

            if ($request->deliveryman_id) {
                $query->where('assigned_deliveryman_id', $request->deliveryman_id);
            }

            if ($request->company_id) {
                $query->where(function($q) use ($request) {
                    $q->where('winning_company_id', $request->company_id)
                      ->orWhereHas('assignedDeliveryman', function($subQ) use ($request) {
                          $subQ->where('company_id', $request->company_id);
                      });
                });
            }

            $assignments = $query->orderBy('created_at', 'desc')->paginate(20);

            $assignmentsData = $assignments->getCollection()->map(function($parcel) {
                return [
                    'parcel' => [
                        'id' => $parcel->id,
                        'tracking_id' => $parcel->tracking_id,
                        'is_bid_parcel' => $parcel->is_bid_parcel,
                        'bid_status' => $parcel->bid_status,
                        'status' => $parcel->status,
                        'created_at' => $parcel->created_at
                    ],
                    'deliveryman' => [
                        'id' => $parcel->assignedDeliveryman->id ?? null,
                        'name' => $parcel->assignedDeliveryman->name ?? 'N/A',
                        'phone' => $parcel->assignedDeliveryman->phone ?? 'N/A'
                    ],
                    'company' => [
                        'id' => $parcel->assignedDeliveryman->company->id ?? $parcel->winning_company_id,
                        'name' => $parcel->assignedDeliveryman->company->name ?? $parcel->winningCompany->name ?? 'N/A'
                    ],
                    'merchant' => [
                        'id' => $parcel->merchant->id ?? null,
                        'name' => $parcel->merchant->business_name ?? 'N/A'
                    ]
                ];
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'assignments' => $assignmentsData,
                    'pagination' => [
                        'current_page' => $assignments->currentPage(),
                        'last_page' => $assignments->lastPage(),
                        'per_page' => $assignments->perPage(),
                        'total' => $assignments->total()
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get assignment history: ' . $e->getMessage()
            ], 500);
        }
    }
}
