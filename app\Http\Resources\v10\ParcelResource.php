<?php

namespace App\Http\Resources\v10;

use Illuminate\Http\Resources\Json\JsonResource;

class ParcelResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {

        return [
            "id" => $this->id,
            "tracking_id" => $this->tracking_id,
            "merchant_id" => $this->merchant_id,
            "merchant_name" => $this->merchant->business_name,
            "merchant_user_name" => $this->merchant->user->name,
            "merchant_user_email" => $this->merchant->user->email,
            "merchant_mobile" => $this->merchant->user->mobile,
            "merchant_address" => $this->merchant->address,
            "customer_name" => $this->customer_name,
            "customer_phone" => (string) $this->customer_phone,
            "customer_address" => $this->customer_address,
            "invoice_no" => (string) $this->invoice_no,
            "weight" => (string) $this->weight . ' ' . optional($this->deliveryCategory)->title,
            "total_delivery_amount" => $this->total_delivery_amount,
            "cod_amount" => $this->cod_amount,
            "vat_amount" => $this->vat_amount,
            "current_payable" => $this->current_payable,
            "cash_collection" => $this->cash_collection,
            "delivery_type_id" => (int) $this->delivery_type_id,
            "deliveryType" => trans("deliveryType." . $this->delivery_type_id),
            "status" => (int) $this->status,
            "statusName" => trans("parcelStatus." . $this->status),
            'pickup_date' => dateFormat($this->pickup_date),
            'delivery_date' => dateFormat($this->delivery_date),
            'created_at' => $this->created_at->format('d M Y, h:i A'),
            'updated_at' => $this->updated_at->format('d M Y, h:i A'),
            'parcel_date' => dateFormat($this->created_at),
            'parcel_time' => date('h:i a', strtotime($this->created_at)),

            // Bidding information
            'is_bid_parcel' => (bool) $this->is_bid_parcel,
            'bid_status' => $this->bid_status,
            'offered_delivery_charge' => $this->offered_delivery_charge,
            'accepted_delivery_charge' => $this->accepted_delivery_charge,
            'bid_timeout_at' => $this->bid_timeout_at ? $this->bid_timeout_at->format('d M Y, h:i A') : null,
            'bid_requirements' => $this->bid_requirements,
            'estimated_pickup_time' => $this->estimated_pickup_time,
            'minimum_bid_amount' => $this->minimum_bid_amount,
            'maximum_bid_amount' => $this->maximum_bid_amount,
            'winning_company_id' => $this->winning_company_id,
            'active_bids_count' => $this->when($this->is_bid_parcel, function () {
                return $this->companyBids()->where('status', 'pending')->count();
            }),
            'lowest_bid' => $this->when($this->is_bid_parcel, function () {
                return $this->companyBids()->where('status', 'pending')->min('offered_charge');
            }),
            'highest_bid' => $this->when($this->is_bid_parcel, function () {
                return $this->companyBids()->where('status', 'pending')->max('offered_charge');
            }),
            'winning_company' => $this->when($this->winning_company_id, function () {
                return [
                    'id' => $this->winningCompany->id ?? null,
                    'name' => $this->winningCompany->name ?? null,
                    'phone' => $this->winningCompany->phone ?? null
                ];
            }),
            'assigned_deliveryman' => $this->when($this->assigned_deliveryman_id, function () {
                return [
                    'id' => $this->assignedDeliveryman->id ?? null,
                    'name' => $this->assignedDeliveryman->name ?? null,
                    'phone' => $this->assignedDeliveryman->phone ?? null,
                    'current_location' => [
                        'lat' => $this->assignedDeliveryman->current_lat ?? null,
                        'lng' => $this->assignedDeliveryman->current_lng ?? null
                    ]
                ];
            }),
        ];
    }

}
