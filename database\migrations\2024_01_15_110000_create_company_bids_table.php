<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('company_bids', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->constrained('general_settings')->onUpdate('cascade')->onDelete('cascade');
            $table->foreignId('parcel_id')->constrained('parcels')->onUpdate('cascade')->onDelete('cascade');
            $table->foreignId('hub_id')->constrained('hubs')->onUpdate('cascade')->onDelete('cascade');
            
            // Bid details
            $table->decimal('offered_charge', 13, 2);
            $table->integer('estimated_pickup_time')->nullable()->comment('Minutes from now');
            $table->integer('estimated_delivery_time')->nullable()->comment('Total delivery time in minutes');
            $table->text('message')->nullable()->comment('Additional message from company');
            
            // Contact information
            $table->string('contact_person')->nullable();
            $table->string('contact_phone', 20)->nullable();
            $table->string('contact_email')->nullable();
            
            // Bid status and timing
            $table->enum('status', ['pending', 'accepted', 'rejected', 'expired', 'withdrawn'])->default('pending');
            $table->timestamp('bid_expires_at')->nullable();
            $table->timestamp('accepted_at')->nullable();
            $table->timestamp('rejected_at')->nullable();
            
            // Additional bid metadata
            $table->json('service_features')->nullable()->comment('JSON array of service features offered');
            $table->decimal('insurance_coverage', 13, 2)->nullable()->comment('Insurance coverage amount');
            $table->boolean('priority_delivery')->default(false);
            $table->text('terms_conditions')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['parcel_id', 'status']);
            $table->index(['company_id', 'status']);
            $table->index(['hub_id', 'status']);
            $table->index('bid_expires_at');
            
            // Unique constraint - one bid per company per parcel
            $table->unique(['parcel_id', 'company_id'], 'unique_company_parcel_bid');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('company_bids');
    }
};
