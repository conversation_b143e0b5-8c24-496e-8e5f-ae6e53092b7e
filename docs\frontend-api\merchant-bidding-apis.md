# Merchant Frontend API Calls - Bidding System

## Overview
This document outlines all API endpoints that merchant frontend applications (web panel, mobile app) need to integrate for the bidding system functionality.

## Base Configuration
```javascript
const API_BASE_URL = 'https://your-domain.com/api/v10';
const API_HEADERS = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Authorization': 'Bearer ' + localStorage.getItem('auth_token'),
    'X-API-KEY': 'your-api-key'
};
```

## 1. Parcel Creation APIs

### 1.1 Create Bid Parcel
**Endpoint:** `POST /merchant/bidding/parcel/create`
**Purpose:** Create a new parcel open for bidding

```javascript
// API Call Example
const createBidParcel = async (parcelData) => {
    const response = await fetch(`${API_BASE_URL}/merchant/bidding/parcel/create`, {
        method: 'POST',
        headers: API_HEADERS,
        body: JSON.stringify({
            // Basic parcel fields
            merchant_shop_id: parcelData.shopId,
            pickup_address: parcelData.pickupAddress,
            pickup_phone: parcelData.pickupPhone,
            customer_name: parcelData.customerName,
            customer_phone: parcelData.customerPhone,
            customer_address: parcelData.customerAddress,
            invoice_no: parcelData.invoiceNo,
            category_id: parcelData.categoryId,
            weight: parcelData.weight,
            delivery_type_id: parcelData.deliveryTypeId,
            cash_collection: parcelData.cashCollection,
            note: parcelData.note,
            
            // Bidding specific fields
            is_bid_parcel: 1,
            offered_delivery_charge: parcelData.offeredCharge,
            minimum_bid_amount: parcelData.minBid,
            maximum_bid_amount: parcelData.maxBid,
            bid_requirements: parcelData.requirements,
            estimated_pickup_time: parcelData.pickupTime,
            bid_timeout_hours: parcelData.timeoutHours,
            priority_delivery: parcelData.isPriority,
            insurance_required: parcelData.needsInsurance
        })
    });
    
    return await response.json();
};

// Usage in React/Vue Component
const handleCreateBidParcel = async (formData) => {
    try {
        const result = await createBidParcel(formData);
        if (result.success) {
            showSuccessMessage('Bid parcel created successfully');
            // Redirect to parcel details or list
            router.push(`/parcels/${result.data.parcel.id}`);
        } else {
            showErrorMessage(result.message);
        }
    } catch (error) {
        showErrorMessage('Failed to create bid parcel');
    }
};
```

### 1.2 Get Hub Companies
**Endpoint:** `GET /merchant/bidding/hub/{hubId}/companies`
**Purpose:** Get list of companies in a hub for bidding preview

```javascript
const getHubCompanies = async (hubId) => {
    const response = await fetch(`${API_BASE_URL}/merchant/bidding/hub/${hubId}/companies`, {
        method: 'GET',
        headers: API_HEADERS
    });
    
    return await response.json();
};

// Usage for showing available companies before creating bid parcel
const loadHubCompanies = async (selectedHubId) => {
    const result = await getHubCompanies(selectedHubId);
    if (result.success) {
        setAvailableCompanies(result.data.companies);
        setBiddingEligible(result.data.bidding_enabled_companies > 0);
    }
};
```

## 2. Bid Management APIs

### 2.1 Get Company Bids for Parcel
**Endpoint:** `GET /merchant/bidding/parcel/{id}/company-bids`
**Purpose:** View all bids received for a parcel

```javascript
const getParcelBids = async (parcelId, filters = {}) => {
    const queryParams = new URLSearchParams({
        status: filters.status || 'all',
        sort_by: filters.sortBy || 'offered_charge',
        sort_order: filters.sortOrder || 'asc',
        include_company_details: true
    });
    
    const response = await fetch(
        `${API_BASE_URL}/merchant/bidding/parcel/${parcelId}/company-bids?${queryParams}`,
        {
            method: 'GET',
            headers: API_HEADERS
        }
    );
    
    return await response.json();
};

// Real-time bid updates using polling or WebSocket
const setupBidUpdates = (parcelId, callback) => {
    const interval = setInterval(async () => {
        const result = await getParcelBids(parcelId);
        if (result.success) {
            callback(result.data.bids);
        }
    }, 5000); // Poll every 5 seconds
    
    return () => clearInterval(interval);
};
```

### 2.2 Accept Company Bid
**Endpoint:** `POST /merchant/bidding/parcel/{id}/accept-company-bid`
**Purpose:** Accept a specific company's bid

```javascript
const acceptCompanyBid = async (parcelId, bidId, reason = '') => {
    const response = await fetch(
        `${API_BASE_URL}/merchant/bidding/parcel/${parcelId}/accept-company-bid`,
        {
            method: 'POST',
            headers: API_HEADERS,
            body: JSON.stringify({
                bid_id: bidId,
                acceptance_reason: reason
            })
        }
    );
    
    return await response.json();
};

// Usage with confirmation dialog
const handleAcceptBid = async (bid) => {
    const confirmed = await showConfirmDialog(
        `Accept bid of $${bid.offered_charge} from ${bid.company.name}?`
    );
    
    if (confirmed) {
        const result = await acceptCompanyBid(bid.parcel_id, bid.id);
        if (result.success) {
            showSuccessMessage('Bid accepted successfully');
            refreshBidsList();
        }
    }
};
```

### 2.3 Cancel Bid Parcel
**Endpoint:** `POST /merchant/bidding/parcel/{id}/cancel`
**Purpose:** Cancel bidding for a parcel

```javascript
const cancelBidParcel = async (parcelId, reason) => {
    const response = await fetch(
        `${API_BASE_URL}/merchant/bidding/parcel/${parcelId}/cancel`,
        {
            method: 'POST',
            headers: API_HEADERS,
            body: JSON.stringify({
                cancellation_reason: reason
            })
        }
    );
    
    return await response.json();
};
```

## 3. Parcel Management APIs

### 3.1 Get Parcel List (Enhanced for Bidding)
**Endpoint:** `GET /parcels`
**Purpose:** Get merchant's parcels with bidding information

```javascript
const getMerchantParcels = async (filters = {}) => {
    const queryParams = new URLSearchParams({
        page: filters.page || 1,
        per_page: filters.perPage || 20,
        status: filters.status || 'all',
        is_bid_parcel: filters.isBidParcel || 'all',
        bid_status: filters.bidStatus || 'all',
        date_from: filters.dateFrom || '',
        date_to: filters.dateTo || '',
        search: filters.search || ''
    });
    
    const response = await fetch(`${API_BASE_URL}/parcels?${queryParams}`, {
        method: 'GET',
        headers: API_HEADERS
    });
    
    return await response.json();
};

// Enhanced parcel list component
const ParcelList = () => {
    const [parcels, setParcels] = useState([]);
    const [filters, setFilters] = useState({});
    
    useEffect(() => {
        loadParcels();
    }, [filters]);
    
    const loadParcels = async () => {
        const result = await getMerchantParcels(filters);
        if (result.success) {
            setParcels(result.data);
        }
    };
    
    return (
        <div>
            {/* Filter controls */}
            <div className="filters">
                <select onChange={(e) => setFilters({...filters, isBidParcel: e.target.value})}>
                    <option value="all">All Parcels</option>
                    <option value="1">Bid Parcels Only</option>
                    <option value="0">Normal Parcels Only</option>
                </select>
                
                <select onChange={(e) => setFilters({...filters, bidStatus: e.target.value})}>
                    <option value="all">All Bid Status</option>
                    <option value="open">Open for Bidding</option>
                    <option value="closed">Bidding Closed</option>
                    <option value="expired">Bidding Expired</option>
                </select>
            </div>
            
            {/* Parcel list */}
            <div className="parcel-list">
                {parcels.map(parcel => (
                    <ParcelCard key={parcel.id} parcel={parcel} />
                ))}
            </div>
        </div>
    );
};
```

### 3.2 Get Parcel Details
**Endpoint:** `GET /parcels/{id}`
**Purpose:** Get detailed parcel information including bidding data

```javascript
const getParcelDetails = async (parcelId) => {
    const response = await fetch(`${API_BASE_URL}/parcels/${parcelId}?include_bids=true`, {
        method: 'GET',
        headers: API_HEADERS
    });
    
    return await response.json();
};
```

## 4. Dashboard and Analytics APIs

### 4.1 Get Bidding Statistics
**Endpoint:** `GET /dashboard/bidding-stats`
**Purpose:** Get merchant's bidding performance metrics

```javascript
const getBiddingStats = async (period = 30) => {
    const response = await fetch(
        `${API_BASE_URL}/dashboard/bidding-stats?days=${period}`,
        {
            method: 'GET',
            headers: API_HEADERS
        }
    );
    
    return await response.json();
};

// Dashboard component
const BiddingDashboard = () => {
    const [stats, setStats] = useState(null);
    
    useEffect(() => {
        loadStats();
    }, []);
    
    const loadStats = async () => {
        const result = await getBiddingStats();
        if (result.success) {
            setStats(result.data);
        }
    };
    
    return (
        <div className="bidding-dashboard">
            <div className="stats-cards">
                <div className="stat-card">
                    <h3>Total Bid Parcels</h3>
                    <p>{stats?.total_bid_parcels || 0}</p>
                </div>
                <div className="stat-card">
                    <h3>Average Bids per Parcel</h3>
                    <p>{stats?.avg_bids_per_parcel || 0}</p>
                </div>
                <div className="stat-card">
                    <h3>Cost Savings</h3>
                    <p>${stats?.total_savings || 0}</p>
                </div>
            </div>
        </div>
    );
};
```

## 5. Settings and Configuration APIs

### 5.1 Get Bidding Settings
**Endpoint:** `GET /bidding/settings/status`
**Purpose:** Check if bidding is enabled and get constraints

```javascript
const getBiddingStatus = async () => {
    const response = await fetch(`${API_BASE_URL}/bidding/settings/status`, {
        method: 'GET',
        headers: API_HEADERS
    });
    
    return await response.json();
};

// Use in parcel creation form to show/hide bidding options
const ParcelForm = () => {
    const [biddingEnabled, setBiddingEnabled] = useState(false);
    const [biddingConstraints, setBiddingConstraints] = useState({});
    
    useEffect(() => {
        checkBiddingStatus();
    }, []);
    
    const checkBiddingStatus = async () => {
        const result = await getBiddingStatus();
        if (result.success) {
            setBiddingEnabled(result.data.system_enabled);
            setBiddingConstraints(result.data.constraints);
        }
    };
    
    return (
        <form>
            {/* Regular parcel fields */}
            
            {biddingEnabled && (
                <div className="bidding-section">
                    <label>
                        <input type="checkbox" name="is_bid_parcel" />
                        Enable bidding for this parcel
                    </label>
                    
                    {/* Bidding specific fields */}
                </div>
            )}
        </form>
    );
};
```

## 6. Error Handling and Validation

```javascript
// Centralized error handling
const handleApiError = (error, context = '') => {
    console.error(`API Error in ${context}:`, error);
    
    if (error.status === 401) {
        // Redirect to login
        window.location.href = '/login';
    } else if (error.status === 403) {
        showErrorMessage('You do not have permission to perform this action');
    } else if (error.status === 422) {
        // Validation errors
        showValidationErrors(error.errors);
    } else {
        showErrorMessage(error.message || 'An unexpected error occurred');
    }
};

// Validation helper for bid amounts
const validateBidAmount = async (amount, offeredCharge) => {
    const response = await fetch(`${API_BASE_URL}/bidding/settings/validate-bid-amount`, {
        method: 'POST',
        headers: API_HEADERS,
        body: JSON.stringify({
            bid_amount: amount,
            offered_amount: offeredCharge
        })
    });
    
    return await response.json();
};
```

## 7. Real-time Features

```javascript
// WebSocket connection for real-time bid updates
const setupWebSocket = (parcelId, onBidUpdate) => {
    const ws = new WebSocket(`wss://your-domain.com/ws/bidding/${parcelId}`);
    
    ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        if (data.type === 'new_bid') {
            onBidUpdate(data.bid);
        }
    };
    
    return ws;
};

// Push notification handling
const setupPushNotifications = () => {
    if ('serviceWorker' in navigator && 'PushManager' in window) {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => {
                // Subscribe to push notifications
                return registration.pushManager.subscribe({
                    userVisibleOnly: true,
                    applicationServerKey: 'your-vapid-key'
                });
            })
            .then(subscription => {
                // Send subscription to server
                return fetch(`${API_BASE_URL}/push-subscriptions`, {
                    method: 'POST',
                    headers: API_HEADERS,
                    body: JSON.stringify(subscription)
                });
            });
    }
};
```

This completes the merchant frontend API documentation. The next section will cover company frontend APIs.
