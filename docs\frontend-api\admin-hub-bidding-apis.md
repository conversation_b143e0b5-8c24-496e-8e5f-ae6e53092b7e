# Admin/Hub Frontend API Calls - Bidding System

## Overview
This document outlines all API endpoints that admin and hub management frontend applications need to integrate for managing the bidding system.

## Base Configuration
```javascript
const API_BASE_URL = 'https://your-domain.com/api/v10';
const API_HEADERS = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Authorization': 'Bearer ' + localStorage.getItem('admin_auth_token'),
    'X-API-KEY': 'your-api-key'
};
```

## 1. Hub Management APIs

### 1.1 Get Hub Companies
**Endpoint:** `GET /hub/bidding/{hubId}/companies`
**Purpose:** Get all companies in a hub with bidding information

```javascript
const getHubCompanies = async (hubId) => {
    const response = await fetch(
        `${API_BASE_URL}/hub/bidding/${hubId}/companies`,
        {
            method: 'GET',
            headers: API_HEADERS
        }
    );
    
    return await response.json();
};

// Hub companies management component
const HubCompaniesManagement = ({ hubId }) => {
    const [companies, setCompanies] = useState([]);
    const [loading, setLoading] = useState(true);
    
    useEffect(() => {
        loadHubCompanies();
    }, [hubId]);
    
    const loadHubCompanies = async () => {
        try {
            const result = await getHubCompanies(hubId);
            if (result.success) {
                setCompanies(result.data.companies);
            }
        } catch (error) {
            showErrorMessage('Failed to load hub companies');
        } finally {
            setLoading(false);
        }
    };
    
    return (
        <div className="hub-companies">
            <div className="header">
                <h2>Hub Companies - Bidding Management</h2>
                <div className="summary">
                    <span>Total: {result.data.total_companies}</span>
                    <span>Bidding Enabled: {result.data.bidding_enabled_companies}</span>
                </div>
            </div>
            
            <div className="companies-table">
                <table>
                    <thead>
                        <tr>
                            <th>Company Name</th>
                            <th>Bidding Status</th>
                            <th>Auto Bid</th>
                            <th>Available Delivery Men</th>
                            <th>Success Rate</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {companies.map(company => (
                            <CompanyRow 
                                key={company.id} 
                                company={company} 
                                onUpdate={loadHubCompanies}
                            />
                        ))}
                    </tbody>
                </table>
            </div>
        </div>
    );
};

const CompanyRow = ({ company, onUpdate }) => {
    const toggleBidding = async () => {
        const result = await toggleCompanyBidding(company.hub_id, company.id, !company.bidding_enabled);
        if (result.success) {
            onUpdate();
        }
    };
    
    return (
        <tr>
            <td>{company.name}</td>
            <td>
                <span className={`status ${company.bidding_enabled ? 'enabled' : 'disabled'}`}>
                    {company.bidding_enabled ? 'Enabled' : 'Disabled'}
                </span>
            </td>
            <td>
                <span className={`status ${company.auto_bid_enabled ? 'enabled' : 'disabled'}`}>
                    {company.auto_bid_enabled ? 'Yes' : 'No'}
                </span>
            </td>
            <td>{company.metrics.available_delivery_men}</td>
            <td>{company.metrics.success_rate}%</td>
            <td>
                <button onClick={toggleBidding}>
                    {company.bidding_enabled ? 'Disable' : 'Enable'} Bidding
                </button>
                <button onClick={() => viewCompanyDetails(company.id)}>
                    View Details
                </button>
            </td>
        </tr>
    );
};
```

### 1.2 Toggle Company Bidding
**Endpoint:** `POST /hub/bidding/{hubId}/company/{companyId}/toggle-bidding`
**Purpose:** Enable or disable bidding for a specific company

```javascript
const toggleCompanyBidding = async (hubId, companyId, enabled) => {
    const response = await fetch(
        `${API_BASE_URL}/hub/bidding/${hubId}/company/${companyId}/toggle-bidding`,
        {
            method: 'POST',
            headers: API_HEADERS,
            body: JSON.stringify({
                bidding_enabled: enabled
            })
        }
    );
    
    return await response.json();
};

// Bulk toggle component
const BulkCompanyActions = ({ hubId, selectedCompanies, onUpdate }) => {
    const handleBulkToggle = async (enabled) => {
        const promises = selectedCompanies.map(companyId => 
            toggleCompanyBidding(hubId, companyId, enabled)
        );
        
        try {
            await Promise.all(promises);
            showSuccessMessage(`Bidding ${enabled ? 'enabled' : 'disabled'} for ${selectedCompanies.length} companies`);
            onUpdate();
        } catch (error) {
            showErrorMessage('Failed to update some companies');
        }
    };
    
    return (
        <div className="bulk-actions">
            <button onClick={() => handleBulkToggle(true)}>
                Enable Bidding for Selected
            </button>
            <button onClick={() => handleBulkToggle(false)}>
                Disable Bidding for Selected
            </button>
        </div>
    );
};
```

### 1.3 Get Hub Bidding Statistics
**Endpoint:** `GET /hub/bidding/{hubId}/statistics`
**Purpose:** Get comprehensive bidding statistics for a hub

```javascript
const getHubBiddingStatistics = async (hubId, days = 30) => {
    const response = await fetch(
        `${API_BASE_URL}/hub/bidding/${hubId}/statistics?days=${days}`,
        {
            method: 'GET',
            headers: API_HEADERS
        }
    );
    
    return await response.json();
};

// Hub statistics dashboard
const HubBiddingDashboard = ({ hubId }) => {
    const [statistics, setStatistics] = useState(null);
    const [period, setPeriod] = useState(30);
    const [loading, setLoading] = useState(true);
    
    useEffect(() => {
        loadStatistics();
    }, [hubId, period]);
    
    const loadStatistics = async () => {
        try {
            const result = await getHubBiddingStatistics(hubId, period);
            if (result.success) {
                setStatistics(result.data);
            }
        } catch (error) {
            showErrorMessage('Failed to load statistics');
        } finally {
            setLoading(false);
        }
    };
    
    return (
        <div className="hub-dashboard">
            <div className="dashboard-header">
                <h2>Hub Bidding Dashboard</h2>
                <select value={period} onChange={(e) => setPeriod(e.target.value)}>
                    <option value="7">Last 7 days</option>
                    <option value="30">Last 30 days</option>
                    <option value="90">Last 90 days</option>
                </select>
            </div>
            
            {/* Overview Statistics */}
            <div className="stats-grid">
                <StatCard 
                    title="Total Bid Parcels" 
                    value={statistics?.time_based_stats.bid_parcels_created}
                    icon="package"
                />
                <StatCard 
                    title="Total Bids Placed" 
                    value={statistics?.time_based_stats.bids_placed}
                    icon="trending-up"
                />
                <StatCard 
                    title="Bids Won" 
                    value={statistics?.time_based_stats.bids_won}
                    icon="award"
                />
                <StatCard 
                    title="Total Bid Value" 
                    value={`$${statistics?.time_based_stats.total_bid_value}`}
                    icon="dollar-sign"
                />
                <StatCard 
                    title="Average Bid Amount" 
                    value={`$${statistics?.time_based_stats.average_bid_amount}`}
                    icon="bar-chart"
                />
                <StatCard 
                    title="Hub Commission" 
                    value={`$${statistics?.hub_overview.total_commission}`}
                    icon="percent"
                />
            </div>
            
            {/* Charts */}
            <div className="charts-section">
                <div className="chart-container">
                    <h3>Daily Bidding Activity</h3>
                    <DailyActivityChart data={statistics?.daily_stats} />
                </div>
                
                <div className="chart-container">
                    <h3>Company Performance Ranking</h3>
                    <CompanyRankingChart data={statistics?.company_ranking} />
                </div>
            </div>
            
            {/* Company Performance Table */}
            <div className="performance-table">
                <h3>Company Performance</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Company</th>
                            <th>Total Bids</th>
                            <th>Won Bids</th>
                            <th>Success Rate</th>
                            <th>Total Value</th>
                        </tr>
                    </thead>
                    <tbody>
                        {statistics?.company_ranking.map(company => (
                            <tr key={company.company_id}>
                                <td>{company.company_name}</td>
                                <td>{company.total_bids}</td>
                                <td>{company.won_bids}</td>
                                <td>{company.success_rate}%</td>
                                <td>${company.total_value}</td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </div>
    );
};
```

### 1.4 Get Active Bid Parcels
**Endpoint:** `GET /hub/bidding/{hubId}/active-parcels`
**Purpose:** Get all active bid parcels in a hub

```javascript
const getActiveBidParcels = async (hubId, page = 1) => {
    const response = await fetch(
        `${API_BASE_URL}/hub/bidding/${hubId}/active-parcels?page=${page}`,
        {
            method: 'GET',
            headers: API_HEADERS
        }
    );
    
    return await response.json();
};

// Active parcels monitoring
const ActiveBidParcels = ({ hubId }) => {
    const [parcels, setParcels] = useState([]);
    const [pagination, setPagination] = useState({});
    
    useEffect(() => {
        loadActiveParcels();
        
        // Auto-refresh every 30 seconds
        const interval = setInterval(loadActiveParcels, 30000);
        return () => clearInterval(interval);
    }, [hubId]);
    
    const loadActiveParcels = async () => {
        const result = await getActiveBidParcels(hubId);
        if (result.success) {
            setParcels(result.data.parcels);
            setPagination(result.data.pagination);
        }
    };
    
    return (
        <div className="active-parcels">
            <div className="header">
                <h3>Active Bid Parcels</h3>
                <button onClick={loadActiveParcels}>Refresh</button>
            </div>
            
            <div className="parcels-grid">
                {parcels.map(parcel => (
                    <div key={parcel.id} className="parcel-card">
                        <div className="parcel-header">
                            <span className="tracking-id">#{parcel.tracking_id}</span>
                            <span className="time-remaining">
                                {parcel.time_remaining_hours}h remaining
                            </span>
                        </div>
                        
                        <div className="parcel-info">
                            <p><strong>Merchant:</strong> {parcel.merchant_name}</p>
                            <p><strong>Offered Amount:</strong> ${parcel.offered_delivery_charge}</p>
                            <p><strong>Active Bids:</strong> {parcel.active_bids_count}</p>
                            <p><strong>Lowest Bid:</strong> ${parcel.lowest_bid}</p>
                            <p><strong>Highest Bid:</strong> ${parcel.highest_bid}</p>
                        </div>
                        
                        <div className="parcel-actions">
                            <button onClick={() => viewParcelDetails(parcel.id)}>
                                View Details
                            </button>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};
```

## 2. System Settings APIs

### 2.1 Get Bidding Settings
**Endpoint:** `GET /bidding/settings/`
**Purpose:** Get all bidding system settings

```javascript
const getBiddingSettings = async (category = 'all') => {
    const response = await fetch(
        `${API_BASE_URL}/bidding/settings/?category=${category}`,
        {
            method: 'GET',
            headers: API_HEADERS
        }
    );
    
    return await response.json();
};

// Settings management component
const BiddingSettingsManager = () => {
    const [settings, setSettings] = useState({});
    const [activeTab, setActiveTab] = useState('core');
    const [loading, setLoading] = useState(true);
    
    const tabs = [
        { id: 'core', label: 'Core Settings' },
        { id: 'constraints', label: 'Bid Constraints' },
        { id: 'notifications', label: 'Notifications' },
        { id: 'performance', label: 'Performance' },
        { id: 'security', label: 'Security' }
    ];
    
    useEffect(() => {
        loadSettings();
    }, [activeTab]);
    
    const loadSettings = async () => {
        const result = await getBiddingSettings(activeTab);
        if (result.success) {
            setSettings(result.data.settings);
        }
        setLoading(false);
    };
    
    return (
        <div className="settings-manager">
            <div className="settings-tabs">
                {tabs.map(tab => (
                    <button 
                        key={tab.id}
                        className={`tab ${activeTab === tab.id ? 'active' : ''}`}
                        onClick={() => setActiveTab(tab.id)}
                    >
                        {tab.label}
                    </button>
                ))}
            </div>
            
            <div className="settings-content">
                {activeTab === 'core' && <CoreSettings settings={settings} onUpdate={loadSettings} />}
                {activeTab === 'constraints' && <ConstraintSettings settings={settings} onUpdate={loadSettings} />}
                {activeTab === 'notifications' && <NotificationSettings settings={settings} onUpdate={loadSettings} />}
                {activeTab === 'performance' && <PerformanceSettings settings={settings} onUpdate={loadSettings} />}
                {activeTab === 'security' && <SecuritySettings settings={settings} onUpdate={loadSettings} />}
            </div>
        </div>
    );
};

// Core settings component
const CoreSettings = ({ settings, onUpdate }) => {
    const [formData, setFormData] = useState(settings);
    
    useEffect(() => {
        setFormData(settings);
    }, [settings]);
    
    const handleSave = async () => {
        const result = await updateBiddingSettings([
            { key: 'bidding_enabled', value: formData.bidding_enabled, type: 'boolean' },
            { key: 'bid_timeout_hours', value: formData.bid_timeout_hours, type: 'integer' },
            { key: 'min_companies_per_hub', value: formData.min_companies_per_hub, type: 'integer' },
            { key: 'auto_accept_best_bid', value: formData.auto_accept_best_bid, type: 'boolean' },
            { key: 'hub_commission', value: formData.hub_commission, type: 'decimal' }
        ]);
        
        if (result.success) {
            showSuccessMessage('Settings updated successfully');
            onUpdate();
        }
    };
    
    return (
        <div className="core-settings">
            <div className="setting-group">
                <label>
                    <input 
                        type="checkbox" 
                        checked={formData.bidding_enabled}
                        onChange={(e) => setFormData({...formData, bidding_enabled: e.target.checked})}
                    />
                    Enable Bidding System
                </label>
            </div>
            
            <div className="setting-group">
                <label>Bid Timeout (Hours)</label>
                <input 
                    type="number" 
                    value={formData.bid_timeout_hours}
                    onChange={(e) => setFormData({...formData, bid_timeout_hours: e.target.value})}
                    min="1" 
                    max="168"
                />
            </div>
            
            <div className="setting-group">
                <label>Minimum Companies per Hub</label>
                <input 
                    type="number" 
                    value={formData.min_companies_per_hub}
                    onChange={(e) => setFormData({...formData, min_companies_per_hub: e.target.value})}
                    min="1" 
                    max="20"
                />
            </div>
            
            <div className="setting-group">
                <label>
                    <input 
                        type="checkbox" 
                        checked={formData.auto_accept_best_bid}
                        onChange={(e) => setFormData({...formData, auto_accept_best_bid: e.target.checked})}
                    />
                    Auto-accept Best Bid After Timeout
                </label>
            </div>
            
            <div className="setting-group">
                <label>Hub Commission (%)</label>
                <input 
                    type="number" 
                    value={formData.hub_commission}
                    onChange={(e) => setFormData({...formData, hub_commission: e.target.value})}
                    min="0" 
                    max="50"
                    step="0.1"
                />
            </div>
            
            <button onClick={handleSave} className="save-button">
                Save Settings
            </button>
        </div>
    );
};
```

### 2.2 Update Bidding Settings
**Endpoint:** `PUT /bidding/settings/`
**Purpose:** Update bidding system settings

```javascript
const updateBiddingSettings = async (settingsArray) => {
    const response = await fetch(
        `${API_BASE_URL}/bidding/settings/`,
        {
            method: 'PUT',
            headers: API_HEADERS,
            body: JSON.stringify({
                settings: settingsArray
            })
        }
    );
    
    return await response.json();
};
```

### 2.3 Update Hub Bidding Settings
**Endpoint:** `PUT /hub/bidding/{hubId}/settings`
**Purpose:** Update hub-specific bidding settings

```javascript
const updateHubBiddingSettings = async (hubId, settings) => {
    const response = await fetch(
        `${API_BASE_URL}/hub/bidding/${hubId}/settings`,
        {
            method: 'PUT',
            headers: API_HEADERS,
            body: JSON.stringify(settings)
        }
    );
    
    return await response.json();
};

// Hub-specific settings
const HubSettings = ({ hubId }) => {
    const [settings, setSettings] = useState({
        bid_timeout_hours: 24,
        min_companies_required: 2,
        auto_accept_enabled: false,
        notification_enabled: true
    });
    
    const handleSave = async () => {
        const result = await updateHubBiddingSettings(hubId, settings);
        if (result.success) {
            showSuccessMessage('Hub settings updated successfully');
        }
    };
    
    return (
        <div className="hub-settings">
            <h3>Hub-Specific Bidding Settings</h3>
            
            <div className="setting-group">
                <label>Bid Timeout (Hours)</label>
                <input 
                    type="number" 
                    value={settings.bid_timeout_hours}
                    onChange={(e) => setSettings({...settings, bid_timeout_hours: e.target.value})}
                />
            </div>
            
            <div className="setting-group">
                <label>Minimum Companies Required</label>
                <input 
                    type="number" 
                    value={settings.min_companies_required}
                    onChange={(e) => setSettings({...settings, min_companies_required: e.target.value})}
                />
            </div>
            
            <div className="setting-group">
                <label>
                    <input 
                        type="checkbox" 
                        checked={settings.auto_accept_enabled}
                        onChange={(e) => setSettings({...settings, auto_accept_enabled: e.target.checked})}
                    />
                    Auto-accept Best Bid
                </label>
            </div>
            
            <div className="setting-group">
                <label>
                    <input 
                        type="checkbox" 
                        checked={settings.notification_enabled}
                        onChange={(e) => setSettings({...settings, notification_enabled: e.target.checked})}
                    />
                    Enable Notifications
                </label>
            </div>
            
            <button onClick={handleSave}>Save Hub Settings</button>
        </div>
    );
};
```

## 3. Monitoring and Analytics APIs

### 3.1 Get System-wide Bidding Status
**Endpoint:** `GET /bidding/settings/status`
**Purpose:** Get overall system status and health metrics

```javascript
const getSystemBiddingStatus = async () => {
    const response = await fetch(
        `${API_BASE_URL}/bidding/settings/status`,
        {
            method: 'GET',
            headers: API_HEADERS
        }
    );
    
    return await response.json();
};

// System status dashboard
const SystemStatusDashboard = () => {
    const [status, setStatus] = useState(null);
    
    useEffect(() => {
        loadSystemStatus();
        
        // Refresh every minute
        const interval = setInterval(loadSystemStatus, 60000);
        return () => clearInterval(interval);
    }, []);
    
    const loadSystemStatus = async () => {
        const result = await getSystemBiddingStatus();
        if (result.success) {
            setStatus(result.data);
        }
    };
    
    return (
        <div className="system-status">
            <div className="status-header">
                <h2>Bidding System Status</h2>
                <div className={`status-indicator ${status?.system_enabled ? 'online' : 'offline'}`}>
                    {status?.system_enabled ? 'Online' : 'Offline'}
                </div>
            </div>
            
            <div className="status-grid">
                <div className="status-card">
                    <h3>System Health</h3>
                    <p>Bidding Enabled: {status?.system_enabled ? 'Yes' : 'No'}</p>
                    <p>Notifications: {status?.notifications_enabled ? 'Yes' : 'No'}</p>
                    <p>Auto Accept: {status?.auto_accept_enabled ? 'Yes' : 'No'}</p>
                </div>
                
                <div className="status-card">
                    <h3>Current Constraints</h3>
                    <p>Min Bid: {status?.constraints.min_percentage}%</p>
                    <p>Max Bid: {status?.constraints.max_percentage}%</p>
                    <p>Timeout: {status?.timeouts.bid_timeout_hours}h</p>
                </div>
                
                <div className="status-card">
                    <h3>Active Features</h3>
                    <p>Suggestions: {status?.features.suggestions ? 'On' : 'Off'}</p>
                    <p>Auto Bidding: {status?.features.auto_bidding ? 'On' : 'Off'}</p>
                    <p>Analytics: {status?.features.analytics ? 'On' : 'Off'}</p>
                    <p>Priority: {status?.features.priority ? 'On' : 'Off'}</p>
                </div>
            </div>
        </div>
    );
};
```

## 4. Utility Functions

```javascript
// Clear settings cache
const clearSettingsCache = async () => {
    const response = await fetch(
        `${API_BASE_URL}/bidding/settings/clear-cache`,
        {
            method: 'POST',
            headers: API_HEADERS
        }
    );
    
    return await response.json();
};

// Reset settings to defaults
const resetBiddingSettings = async () => {
    const response = await fetch(
        `${API_BASE_URL}/bidding/settings/reset`,
        {
            method: 'POST',
            headers: API_HEADERS
        }
    );
    
    return await response.json();
};

// Export settings
const exportBiddingSettings = async () => {
    const response = await fetch(
        `${API_BASE_URL}/bidding/settings/export`,
        {
            method: 'GET',
            headers: API_HEADERS
        }
    );
    
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'bidding-settings.json';
    a.click();
};

// Import settings
const importBiddingSettings = async (file) => {
    const formData = new FormData();
    formData.append('settings_file', file);
    
    const response = await fetch(
        `${API_BASE_URL}/bidding/settings/import`,
        {
            method: 'POST',
            headers: {
                'Authorization': API_HEADERS.Authorization,
                'X-API-KEY': API_HEADERS['X-API-KEY']
            },
            body: formData
        }
    );
    
    return await response.json();
};
```

This completes the admin/hub frontend API documentation. The next section will cover the API integration guide.
