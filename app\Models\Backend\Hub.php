<?php

namespace App\Models\Backend;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Hub extends Model
{
    use HasFactory, LogsActivity;
    protected $fillable = ['name', 'phone', 'address'];

    // Get all row. Descending order using scope.
    public function scopeOrderByDesc($query, $data)
    {
        $query->orderBy($data, 'desc');
    }

    /**
     * Activity Log
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->useLogName('Hub')
            ->logOnly(['name', 'phone', 'address'])
            ->setDescriptionForEvent(fn(string $eventName) => "{$eventName}");
    }

    public function getMyStatusAttribute()
    {
        return trans('status.' . $this->status);
    }

    public function parcels()
    {
        return $this->hasMany(Parcel::class, 'hub_id', 'id');
    }

    // Bidding relationships
    public function companies()
    {
        return $this->hasMany(GeneralSettings::class, 'hub_id', 'id')
            ->where('bidding_enabled', true);
    }

    public function bidParcels()
    {
        return $this->hasMany(Parcel::class, 'hub_id', 'id')
            ->where('is_bid_parcel', true);
    }

    public function companyBids()
    {
        return $this->hasMany(CompanyBid::class, 'hub_id', 'id');
    }

    public function deliveryMen()
    {
        return $this->hasManyThrough(
            DeliveryMan::class,
            GeneralSettings::class,
            'hub_id', // Foreign key on GeneralSettings table
            'company_id', // Foreign key on DeliveryMan table
            'id', // Local key on Hub table
            'id' // Local key on GeneralSettings table
        );
    }

    // Bidding business logic methods
    public function getActiveBiddingCompaniesCount()
    {
        return $this->companies()->count();
    }

    public function getActiveBidParcelsCount()
    {
        return $this->bidParcels()->where('bid_status', 'open')->count();
    }

    public function getTotalBidsCount()
    {
        return $this->companyBids()->count();
    }

    public function getBiddingStatistics()
    {
        return [
            'active_companies' => $this->getActiveBiddingCompaniesCount(),
            'active_bid_parcels' => $this->getActiveBidParcelsCount(),
            'total_bids' => $this->getTotalBidsCount(),
            'completed_bids' => $this->companyBids()->where('status', 'accepted')->count(),
            'average_bid_amount' => $this->companyBids()
                ->where('status', 'accepted')
                ->avg('offered_charge') ?? 0
        ];
    }

    public function scopeCompanywise($query)
    {
        return $query->where('company_id', settings()->id);
    }
}
