<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('general_settings', function (Blueprint $table) {
            // Hub relationship
            $table->foreignId('hub_id')->nullable()->constrained('hubs')->onDelete('set null')->after('purchase_code');
            
            // Bidding configuration
            $table->boolean('bidding_enabled')->default(true)->after('hub_id');
            $table->boolean('auto_bid_enabled')->default(false)->after('bidding_enabled');
            $table->decimal('min_bid_amount', 13, 2)->default(0)->after('auto_bid_enabled');
            $table->decimal('max_bid_amount', 13, 2)->default(9999.99)->after('min_bid_amount');
            
            // Company bidding preferences
            $table->integer('default_pickup_time')->default(60)->after('max_bid_amount')->comment('Default pickup time in minutes');
            $table->decimal('bidding_commission_rate', 5, 2)->default(5.00)->after('default_pickup_time')->comment('Commission rate for bid parcels');
            $table->boolean('auto_accept_bids')->default(false)->after('bidding_commission_rate');
            $table->decimal('auto_accept_threshold', 13, 2)->nullable()->after('auto_accept_bids')->comment('Auto accept bids below this amount');
            
            // Notification preferences
            $table->boolean('bid_notifications_enabled')->default(true)->after('auto_accept_threshold');
            $table->string('notification_email')->nullable()->after('bid_notifications_enabled');
            $table->string('notification_phone')->nullable()->after('notification_email');
            
            // Service capabilities
            $table->json('service_areas')->nullable()->after('notification_phone')->comment('JSON array of service area codes');
            $table->json('vehicle_types')->nullable()->after('service_areas')->comment('JSON array of available vehicle types');
            $table->decimal('max_parcel_weight', 8, 2)->nullable()->after('vehicle_types')->comment('Maximum parcel weight in kg');
            $table->boolean('fragile_items_accepted')->default(true)->after('max_parcel_weight');
            $table->boolean('liquid_items_accepted')->default(true)->after('fragile_items_accepted');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('general_settings', function (Blueprint $table) {
            $table->dropForeign(['hub_id']);
            $table->dropColumn([
                'hub_id',
                'bidding_enabled',
                'auto_bid_enabled',
                'min_bid_amount',
                'max_bid_amount',
                'default_pickup_time',
                'bidding_commission_rate',
                'auto_accept_bids',
                'auto_accept_threshold',
                'bid_notifications_enabled',
                'notification_email',
                'notification_phone',
                'service_areas',
                'vehicle_types',
                'max_parcel_weight',
                'fragile_items_accepted',
                'liquid_items_accepted'
            ]);
        });
    }
};
