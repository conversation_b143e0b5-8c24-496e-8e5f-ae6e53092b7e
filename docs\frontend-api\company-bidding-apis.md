# Company Frontend API Calls - Bidding System

## Overview
This document outlines all API endpoints that company frontend applications need to integrate for participating in the bidding system.

## Base Configuration
```javascript
const API_BASE_URL = 'https://your-domain.com/api/v10';
const API_HEADERS = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Authorization': 'Bearer ' + localStorage.getItem('company_auth_token'),
    'X-API-KEY': 'your-api-key'
};
```

## 1. Bid Opportunity APIs

### 1.1 Get Available Parcels for Bidding
**Endpoint:** `GET /company/bidding/available-parcels`
**Purpose:** Get list of parcels open for bidding in company's hub

```javascript
const getAvailableParcels = async (filters = {}) => {
    const queryParams = new URLSearchParams({
        page: filters.page || 1,
        per_page: filters.perPage || 20,
        category_id: filters.categoryId || '',
        delivery_type_id: filters.deliveryTypeId || '',
        min_amount: filters.minAmount || '',
        max_amount: filters.maxAmount || '',
        sort_by: filters.sortBy || 'created_at',
        sort_order: filters.sortOrder || 'desc',
        time_remaining: filters.timeRemaining || '' // e.g., '2h' for parcels expiring in 2 hours
    });
    
    const response = await fetch(
        `${API_BASE_URL}/company/bidding/available-parcels?${queryParams}`,
        {
            method: 'GET',
            headers: API_HEADERS
        }
    );
    
    return await response.json();
};

// Real-time updates for new bid opportunities
const setupBidOpportunityUpdates = (callback) => {
    const interval = setInterval(async () => {
        const result = await getAvailableParcels({ per_page: 5 });
        if (result.success) {
            callback(result.data.parcels);
        }
    }, 10000); // Poll every 10 seconds
    
    return () => clearInterval(interval);
};

// Usage in React component
const BidOpportunities = () => {
    const [parcels, setParcels] = useState([]);
    const [filters, setFilters] = useState({});
    const [loading, setLoading] = useState(false);
    
    useEffect(() => {
        loadParcels();
        const cleanup = setupBidOpportunityUpdates(handleNewParcels);
        return cleanup;
    }, [filters]);
    
    const loadParcels = async () => {
        setLoading(true);
        const result = await getAvailableParcels(filters);
        if (result.success) {
            setParcels(result.data.parcels);
        }
        setLoading(false);
    };
    
    const handleNewParcels = (newParcels) => {
        // Show notification for new opportunities
        const unseenParcels = newParcels.filter(p => 
            !parcels.some(existing => existing.id === p.id)
        );
        
        if (unseenParcels.length > 0) {
            showNotification(`${unseenParcels.length} new bid opportunities available!`);
        }
    };
    
    return (
        <div className="bid-opportunities">
            <div className="filters">
                <select onChange={(e) => setFilters({...filters, categoryId: e.target.value})}>
                    <option value="">All Categories</option>
                    <option value="1">Documents</option>
                    <option value="2">Electronics</option>
                    <option value="3">Food</option>
                </select>
                
                <input 
                    type="number" 
                    placeholder="Min Amount"
                    onChange={(e) => setFilters({...filters, minAmount: e.target.value})}
                />
                
                <select onChange={(e) => setFilters({...filters, timeRemaining: e.target.value})}>
                    <option value="">All Time</option>
                    <option value="1h">Expiring in 1 hour</option>
                    <option value="6h">Expiring in 6 hours</option>
                    <option value="24h">Expiring in 24 hours</option>
                </select>
            </div>
            
            <div className="parcel-grid">
                {parcels.map(parcel => (
                    <ParcelBidCard key={parcel.id} parcel={parcel} onBidPlace={loadParcels} />
                ))}
            </div>
        </div>
    );
};
```

### 1.2 Get Parcel Details for Bidding
**Endpoint:** `GET /parcels/{id}` (with bidding context)
**Purpose:** Get detailed information about a specific parcel for bidding

```javascript
const getParcelForBidding = async (parcelId) => {
    const response = await fetch(
        `${API_BASE_URL}/parcels/${parcelId}?include_bids=true&include_competition=true`,
        {
            method: 'GET',
            headers: API_HEADERS
        }
    );
    
    return await response.json();
};

// Parcel detail modal for bidding
const ParcelBidModal = ({ parcelId, onClose, onBidPlaced }) => {
    const [parcel, setParcel] = useState(null);
    const [bidAmount, setBidAmount] = useState('');
    const [estimatedTime, setEstimatedTime] = useState(60);
    
    useEffect(() => {
        loadParcelDetails();
    }, [parcelId]);
    
    const loadParcelDetails = async () => {
        const result = await getParcelForBidding(parcelId);
        if (result.success) {
            setParcel(result.data);
            setBidAmount(result.data.suggested_bid || '');
        }
    };
    
    return (
        <div className="modal">
            <div className="modal-content">
                <h2>Bid on Parcel #{parcel?.tracking_id}</h2>
                
                <div className="parcel-info">
                    <p><strong>From:</strong> {parcel?.pickup_address}</p>
                    <p><strong>To:</strong> {parcel?.customer_address}</p>
                    <p><strong>Weight:</strong> {parcel?.weight}</p>
                    <p><strong>Offered Amount:</strong> ${parcel?.offered_delivery_charge}</p>
                    <p><strong>Time Remaining:</strong> {parcel?.time_remaining_hours}h</p>
                </div>
                
                <div className="competition-info">
                    <p><strong>Active Bids:</strong> {parcel?.active_bids_count}</p>
                    <p><strong>Lowest Bid:</strong> ${parcel?.lowest_bid}</p>
                    <p><strong>Your Suggested Bid:</strong> ${parcel?.suggested_bid}</p>
                </div>
                
                <div className="bid-form">
                    <input 
                        type="number" 
                        value={bidAmount}
                        onChange={(e) => setBidAmount(e.target.value)}
                        placeholder="Your bid amount"
                        min={parcel?.minimum_bid_amount}
                        max={parcel?.maximum_bid_amount}
                    />
                    
                    <input 
                        type="number" 
                        value={estimatedTime}
                        onChange={(e) => setEstimatedTime(e.target.value)}
                        placeholder="Pickup time (minutes)"
                    />
                    
                    <button onClick={handlePlaceBid}>Place Bid</button>
                </div>
            </div>
        </div>
    );
};
```

## 2. Bid Placement APIs

### 2.1 Place Bid on Parcel
**Endpoint:** `POST /company/bidding/parcel/{id}/bid`
**Purpose:** Submit a bid for a specific parcel

```javascript
const placeBid = async (parcelId, bidData) => {
    const response = await fetch(
        `${API_BASE_URL}/company/bidding/parcel/${parcelId}/bid`,
        {
            method: 'POST',
            headers: API_HEADERS,
            body: JSON.stringify({
                offered_charge: bidData.amount,
                estimated_pickup_time: bidData.pickupTime,
                estimated_delivery_time: bidData.deliveryTime,
                message: bidData.message,
                contact_person: bidData.contactPerson,
                contact_phone: bidData.contactPhone,
                service_features: bidData.features,
                insurance_coverage: bidData.insurance,
                priority_delivery: bidData.isPriority,
                terms_conditions: bidData.terms
            })
        }
    );
    
    return await response.json();
};

// Enhanced bid placement with validation
const handlePlaceBid = async (parcelId, formData) => {
    try {
        // Validate bid amount first
        const validation = await validateBidAmount(formData.amount, parcel.offered_delivery_charge);
        if (!validation.valid) {
            showErrorMessage(validation.message);
            return;
        }
        
        const result = await placeBid(parcelId, formData);
        if (result.success) {
            showSuccessMessage('Bid placed successfully!');
            onBidPlaced();
            onClose();
            
            // Track analytics
            trackEvent('bid_placed', {
                parcel_id: parcelId,
                bid_amount: formData.amount,
                competition_level: parcel.active_bids_count
            });
        } else {
            showErrorMessage(result.message);
        }
    } catch (error) {
        handleApiError(error, 'placing bid');
    }
};

// Bid validation
const validateBidAmount = async (amount, offeredCharge) => {
    const response = await fetch(`${API_BASE_URL}/bidding/settings/validate-bid-amount`, {
        method: 'POST',
        headers: API_HEADERS,
        body: JSON.stringify({
            bid_amount: amount,
            offered_amount: offeredCharge
        })
    });
    
    return await response.json();
};
```

### 2.2 Get Bid Suggestions
**Endpoint:** `GET /company/bidding/parcel/{id}/suggestions`
**Purpose:** Get AI-powered bid amount suggestions

```javascript
const getBidSuggestions = async (parcelId) => {
    const response = await fetch(
        `${API_BASE_URL}/company/bidding/parcel/${parcelId}/suggestions`,
        {
            method: 'GET',
            headers: API_HEADERS
        }
    );
    
    return await response.json();
};

// Smart bid suggestion component
const BidSuggestions = ({ parcelId, onSuggestionSelect }) => {
    const [suggestions, setSuggestions] = useState([]);
    
    useEffect(() => {
        loadSuggestions();
    }, [parcelId]);
    
    const loadSuggestions = async () => {
        const result = await getBidSuggestions(parcelId);
        if (result.success) {
            setSuggestions(result.data.suggestions);
        }
    };
    
    return (
        <div className="bid-suggestions">
            <h4>Suggested Bid Amounts</h4>
            {suggestions.map((suggestion, index) => (
                <div key={index} className="suggestion-card" onClick={() => onSuggestionSelect(suggestion.amount)}>
                    <div className="amount">${suggestion.amount}</div>
                    <div className="strategy">{suggestion.strategy}</div>
                    <div className="win-probability">{suggestion.win_probability}% win chance</div>
                </div>
            ))}
        </div>
    );
};
```

## 3. Bid Management APIs

### 3.1 Get Company's Bids
**Endpoint:** `GET /company/bidding/my-bids`
**Purpose:** Get list of all bids placed by the company

```javascript
const getMyBids = async (filters = {}) => {
    const queryParams = new URLSearchParams({
        page: filters.page || 1,
        per_page: filters.perPage || 20,
        status: filters.status || 'all', // pending, accepted, rejected, withdrawn
        date_from: filters.dateFrom || '',
        date_to: filters.dateTo || '',
        sort_by: filters.sortBy || 'created_at',
        sort_order: filters.sortOrder || 'desc'
    });
    
    const response = await fetch(
        `${API_BASE_URL}/company/bidding/my-bids?${queryParams}`,
        {
            method: 'GET',
            headers: API_HEADERS
        }
    );
    
    return await response.json();
};

// Bid management dashboard
const MyBids = () => {
    const [bids, setBids] = useState([]);
    const [statistics, setStatistics] = useState({});
    const [filters, setFilters] = useState({ status: 'all' });
    
    useEffect(() => {
        loadBids();
    }, [filters]);
    
    const loadBids = async () => {
        const result = await getMyBids(filters);
        if (result.success) {
            setBids(result.data.bids);
            setStatistics(result.data.statistics);
        }
    };
    
    return (
        <div className="my-bids">
            <div className="statistics">
                <div className="stat-card">
                    <h3>Total Bids</h3>
                    <p>{statistics.total_bids}</p>
                </div>
                <div className="stat-card">
                    <h3>Won Bids</h3>
                    <p>{statistics.won_bids}</p>
                </div>
                <div className="stat-card">
                    <h3>Success Rate</h3>
                    <p>{statistics.success_rate}%</p>
                </div>
            </div>
            
            <div className="filters">
                <select onChange={(e) => setFilters({...filters, status: e.target.value})}>
                    <option value="all">All Bids</option>
                    <option value="pending">Pending</option>
                    <option value="accepted">Won</option>
                    <option value="rejected">Lost</option>
                    <option value="withdrawn">Withdrawn</option>
                </select>
            </div>
            
            <div className="bids-list">
                {bids.map(bid => (
                    <BidCard key={bid.id} bid={bid} onWithdraw={loadBids} />
                ))}
            </div>
        </div>
    );
};
```

### 3.2 Withdraw Bid
**Endpoint:** `POST /company/bidding/bid/{bidId}/withdraw`
**Purpose:** Withdraw a previously placed bid

```javascript
const withdrawBid = async (bidId, reason = '') => {
    const response = await fetch(
        `${API_BASE_URL}/company/bidding/bid/${bidId}/withdraw`,
        {
            method: 'POST',
            headers: API_HEADERS,
            body: JSON.stringify({
                withdrawal_reason: reason
            })
        }
    );
    
    return await response.json();
};

// Bid withdrawal with confirmation
const handleWithdrawBid = async (bid) => {
    const reason = await showPromptDialog('Reason for withdrawal (optional):');
    
    const confirmed = await showConfirmDialog(
        `Are you sure you want to withdraw your bid of $${bid.offered_charge}?`
    );
    
    if (confirmed) {
        const result = await withdrawBid(bid.id, reason);
        if (result.success) {
            showSuccessMessage('Bid withdrawn successfully');
            onWithdraw();
        }
    }
};
```

## 4. Delivery Management APIs

### 4.1 Assign Delivery Man to Won Parcel
**Endpoint:** `POST /company/bidding/parcel/{id}/assign-deliveryman`
**Purpose:** Assign a delivery man to a parcel after winning the bid

```javascript
const assignDeliveryMan = async (parcelId, deliverymanId) => {
    const response = await fetch(
        `${API_BASE_URL}/company/bidding/parcel/${parcelId}/assign-deliveryman`,
        {
            method: 'POST',
            headers: API_HEADERS,
            body: JSON.stringify({
                deliveryman_id: deliverymanId
            })
        }
    );
    
    return await response.json();
};

// Get available delivery men for assignment
const getAvailableDeliveryMen = async (parcelId) => {
    const response = await fetch(
        `${API_BASE_URL}/parcel/assignment/available-delivery-men?parcel_id=${parcelId}`,
        {
            method: 'GET',
            headers: API_HEADERS
        }
    );
    
    return await response.json();
};

// Delivery man assignment component
const AssignDeliveryMan = ({ parcel, onAssigned }) => {
    const [deliveryMen, setDeliveryMen] = useState([]);
    const [selectedDeliveryMan, setSelectedDeliveryMan] = useState('');
    
    useEffect(() => {
        loadAvailableDeliveryMen();
    }, [parcel.id]);
    
    const loadAvailableDeliveryMen = async () => {
        const result = await getAvailableDeliveryMen(parcel.id);
        if (result.success) {
            setDeliveryMen(result.data.available_delivery_men);
        }
    };
    
    const handleAssign = async () => {
        if (!selectedDeliveryMan) {
            showErrorMessage('Please select a delivery man');
            return;
        }
        
        const result = await assignDeliveryMan(parcel.id, selectedDeliveryMan);
        if (result.success) {
            showSuccessMessage('Delivery man assigned successfully');
            onAssigned();
        }
    };
    
    return (
        <div className="assign-deliveryman">
            <h3>Assign Delivery Man to Parcel #{parcel.tracking_id}</h3>
            
            <div className="delivery-men-list">
                {deliveryMen.map(dm => (
                    <div 
                        key={dm.id} 
                        className={`delivery-man-card ${selectedDeliveryMan === dm.id ? 'selected' : ''}`}
                        onClick={() => setSelectedDeliveryMan(dm.id)}
                    >
                        <div className="name">{dm.name}</div>
                        <div className="rating">Rating: {dm.average_rating}/5</div>
                        <div className="deliveries">Deliveries: {dm.completed_deliveries}</div>
                        <div className="location">
                            Distance: {dm.distance_from_pickup || 'N/A'}
                        </div>
                    </div>
                ))}
            </div>
            
            <button onClick={handleAssign} disabled={!selectedDeliveryMan}>
                Assign Selected Delivery Man
            </button>
        </div>
    );
};
```

## 5. Performance and Analytics APIs

### 5.1 Get Company Bidding Performance
**Endpoint:** `GET /company/bidding/performance`
**Purpose:** Get detailed performance metrics and analytics

```javascript
const getBiddingPerformance = async (period = 30) => {
    const response = await fetch(
        `${API_BASE_URL}/company/bidding/performance?days=${period}`,
        {
            method: 'GET',
            headers: API_HEADERS
        }
    );
    
    return await response.json();
};

// Performance dashboard
const PerformanceDashboard = () => {
    const [performance, setPerformance] = useState(null);
    const [period, setPeriod] = useState(30);
    
    useEffect(() => {
        loadPerformance();
    }, [period]);
    
    const loadPerformance = async () => {
        const result = await getBiddingPerformance(period);
        if (result.success) {
            setPerformance(result.data);
        }
    };
    
    return (
        <div className="performance-dashboard">
            <div className="period-selector">
                <select onChange={(e) => setPeriod(e.target.value)}>
                    <option value="7">Last 7 days</option>
                    <option value="30">Last 30 days</option>
                    <option value="90">Last 90 days</option>
                </select>
            </div>
            
            <div className="metrics-grid">
                <div className="metric-card">
                    <h3>Bid Win Rate</h3>
                    <p className="metric-value">{performance?.win_rate}%</p>
                    <p className="metric-change">
                        {performance?.win_rate_change > 0 ? '+' : ''}
                        {performance?.win_rate_change}% from last period
                    </p>
                </div>
                
                <div className="metric-card">
                    <h3>Average Bid Amount</h3>
                    <p className="metric-value">${performance?.avg_bid_amount}</p>
                </div>
                
                <div className="metric-card">
                    <h3>Total Revenue</h3>
                    <p className="metric-value">${performance?.total_revenue}</p>
                </div>
                
                <div className="metric-card">
                    <h3>Parcels Delivered</h3>
                    <p className="metric-value">{performance?.parcels_delivered}</p>
                </div>
            </div>
            
            <div className="charts">
                <BidTrendsChart data={performance?.daily_trends} />
                <CategoryPerformanceChart data={performance?.category_performance} />
            </div>
        </div>
    );
};
```

## 6. Notification and Real-time Updates

```javascript
// WebSocket connection for real-time bid updates
const setupCompanyWebSocket = (companyId, callbacks) => {
    const ws = new WebSocket(`wss://your-domain.com/ws/company/${companyId}`);
    
    ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        
        switch (data.type) {
            case 'new_bid_opportunity':
                callbacks.onNewOpportunity(data.parcel);
                break;
            case 'bid_accepted':
                callbacks.onBidAccepted(data.bid);
                break;
            case 'bid_rejected':
                callbacks.onBidRejected(data.bid);
                break;
            case 'bidding_closed':
                callbacks.onBiddingClosed(data.parcel);
                break;
        }
    };
    
    return ws;
};

// Push notification setup for mobile apps
const setupPushNotifications = async () => {
    if ('serviceWorker' in navigator && 'PushManager' in window) {
        const registration = await navigator.serviceWorker.register('/sw.js');
        const subscription = await registration.pushManager.subscribe({
            userVisibleOnly: true,
            applicationServerKey: 'your-vapid-key'
        });
        
        // Send subscription to server
        await fetch(`${API_BASE_URL}/push-subscriptions`, {
            method: 'POST',
            headers: API_HEADERS,
            body: JSON.stringify({
                subscription,
                type: 'company_bidding'
            })
        });
    }
};
```

This completes the company frontend API documentation. The next section will cover delivery man frontend APIs.
