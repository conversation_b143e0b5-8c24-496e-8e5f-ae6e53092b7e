<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\Backend\Parcel;
use Carbon\Carbon;

class CheckParcelBiddingStatus
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Get parcel ID from route parameters
        $parcelId = $request->route('id') ?? $request->route('parcelId') ?? $request->input('parcel_id');
        
        if (!$parcelId) {
            return response()->json([
                'success' => false,
                'message' => 'Parcel ID is required',
                'error_code' => 'PARCEL_ID_REQUIRED'
            ], 400);
        }

        $parcel = Parcel::find($parcelId);
        
        if (!$parcel) {
            return response()->json([
                'success' => false,
                'message' => 'Parcel not found',
                'error_code' => 'PARCEL_NOT_FOUND'
            ], 404);
        }

        // Check if parcel is a bid parcel
        if (!$parcel->is_bid_parcel) {
            return response()->json([
                'success' => false,
                'message' => 'This parcel is not open for bidding',
                'error_code' => 'NOT_BID_PARCEL'
            ], 400);
        }

        // Check bidding status based on the action being performed
        $action = $this->determineAction($request);
        
        switch ($action) {
            case 'place_bid':
                if (!$this->canPlaceBid($parcel)) {
                    return $this->getBiddingStatusError($parcel);
                }
                break;
                
            case 'accept_bid':
                if (!$this->canAcceptBid($parcel)) {
                    return $this->getBiddingStatusError($parcel);
                }
                break;
                
            case 'view_bids':
                // Allow viewing bids in most states
                break;
                
            case 'assign_deliveryman':
                if (!$this->canAssignDeliveryman($parcel)) {
                    return $this->getBiddingStatusError($parcel);
                }
                break;
        }

        // Add parcel to request for use in controllers
        $request->merge(['current_parcel' => $parcel]);

        return $next($request);
    }

    /**
     * Determine the action being performed based on the request
     */
    private function determineAction(Request $request)
    {
        $route = $request->route()->getName();
        $method = $request->method();
        $uri = $request->getRequestUri();

        if (str_contains($uri, '/bid') && $method === 'POST') {
            return 'place_bid';
        }
        
        if (str_contains($uri, '/accept-company-bid')) {
            return 'accept_bid';
        }
        
        if (str_contains($uri, '/assign-deliveryman')) {
            return 'assign_deliveryman';
        }
        
        if (str_contains($uri, '/company-bids')) {
            return 'view_bids';
        }

        return 'general';
    }

    /**
     * Check if a bid can be placed on the parcel
     */
    private function canPlaceBid($parcel)
    {
        // Check if bidding is still open
        if ($parcel->bid_status !== 'open') {
            return false;
        }

        // Check if bidding has not expired
        if ($parcel->bid_timeout_at && Carbon::now()->isAfter($parcel->bid_timeout_at)) {
            return false;
        }

        return true;
    }

    /**
     * Check if a bid can be accepted
     */
    private function canAcceptBid($parcel)
    {
        // Must be in open status with active bids
        if ($parcel->bid_status !== 'open') {
            return false;
        }

        // Must have at least one pending bid
        if ($parcel->companyBids()->where('status', 'pending')->count() === 0) {
            return false;
        }

        return true;
    }

    /**
     * Check if deliveryman can be assigned
     */
    private function canAssignDeliveryman($parcel)
    {
        // Must have a winning company
        if (!$parcel->winning_company_id) {
            return false;
        }

        // Must be in closed or assigned status
        if (!in_array($parcel->bid_status, ['closed', 'assigned'])) {
            return false;
        }

        return true;
    }

    /**
     * Get appropriate error response based on parcel status
     */
    private function getBiddingStatusError($parcel)
    {
        $message = 'Action not allowed for current parcel status';
        $errorCode = 'INVALID_PARCEL_STATUS';

        switch ($parcel->bid_status) {
            case 'closed':
                $message = 'Bidding is closed for this parcel';
                $errorCode = 'BIDDING_CLOSED';
                break;
                
            case 'expired':
                $message = 'Bidding has expired for this parcel';
                $errorCode = 'BIDDING_EXPIRED';
                break;
                
            case 'assigned':
                $message = 'Parcel has already been assigned';
                $errorCode = 'PARCEL_ASSIGNED';
                break;
        }

        // Check timeout
        if ($parcel->bid_timeout_at && Carbon::now()->isAfter($parcel->bid_timeout_at)) {
            $message = 'Bidding timeout has passed';
            $errorCode = 'BIDDING_TIMEOUT';
        }

        return response()->json([
            'success' => false,
            'message' => $message,
            'error_code' => $errorCode,
            'parcel_status' => [
                'bid_status' => $parcel->bid_status,
                'timeout_at' => $parcel->bid_timeout_at?->format('Y-m-d H:i:s'),
                'is_expired' => $parcel->bid_timeout_at ? Carbon::now()->isAfter($parcel->bid_timeout_at) : false
            ]
        ], 400);
    }
}
