<?php

namespace App\Helpers\Bidding;

use App\Models\Backend\Parcel;
use App\Models\Backend\CompanyBid;
use App\Models\Backend\GeneralSettings;
use App\Models\Backend\BiddingSetting;
use App\Models\Backend\Hub;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class BiddingValidationHelper
{
    /**
     * Validate if a company can place a bid on a parcel
     */
    public static function canCompanyBid(Parcel $parcel, $companyId)
    {
        try {
            $company = GeneralSettings::find($companyId);
            
            if (!$company) {
                return [
                    'can_bid' => false,
                    'reason' => 'Company not found'
                ];
            }

            // Check if company is in the same hub
            if ($company->hub_id !== $parcel->hub_id) {
                return [
                    'can_bid' => false,
                    'reason' => 'Company not in the same hub as parcel'
                ];
            }

            // Check if bidding is enabled for the company
            if (!$company->bidding_enabled) {
                return [
                    'can_bid' => false,
                    'reason' => 'Bidding is not enabled for this company'
                ];
            }

            // Check if parcel is still open for bidding
            if ($parcel->bid_status !== 'open') {
                return [
                    'can_bid' => false,
                    'reason' => 'Bidding is closed for this parcel'
                ];
            }

            // Check if bidding timeout has passed
            if ($parcel->bid_timeout_at && Carbon::now()->gt($parcel->bid_timeout_at)) {
                return [
                    'can_bid' => false,
                    'reason' => 'Bidding time has expired'
                ];
            }

            // Check if company has already placed a bid
            $existingBid = CompanyBid::where('parcel_id', $parcel->id)
                ->where('company_id', $companyId)
                ->where('status', 'pending')
                ->first();

            if ($existingBid) {
                return [
                    'can_bid' => false,
                    'reason' => 'Company has already placed a bid on this parcel',
                    'existing_bid_id' => $existingBid->id
                ];
            }

            // Check company's current workload
            $workloadCheck = self::validateCompanyWorkload($companyId);
            if (!$workloadCheck['valid']) {
                return [
                    'can_bid' => false,
                    'reason' => $workloadCheck['reason']
                ];
            }

            return [
                'can_bid' => true,
                'reason' => 'Company can place a bid'
            ];

        } catch (\Exception $e) {
            Log::error('Failed to validate company bidding eligibility: ' . $e->getMessage());
            return [
                'can_bid' => false,
                'reason' => 'Validation error occurred'
            ];
        }
    }

    /**
     * Validate company workload
     */
    private static function validateCompanyWorkload($companyId)
    {
        try {
            $settings = BiddingSetting::getSettings();
            $maxActiveBids = $settings['max_active_bids_per_company'] ?? 50;

            $activeBidsCount = CompanyBid::where('company_id', $companyId)
                ->where('status', 'pending')
                ->count();

            if ($activeBidsCount >= $maxActiveBids) {
                return [
                    'valid' => false,
                    'reason' => "Company has reached maximum active bids limit ({$maxActiveBids})"
                ];
            }

            return [
                'valid' => true,
                'reason' => 'Workload is acceptable'
            ];

        } catch (\Exception $e) {
            Log::error('Failed to validate company workload: ' . $e->getMessage());
            return [
                'valid' => false,
                'reason' => 'Workload validation error'
            ];
        }
    }

    /**
     * Validate if a merchant can create a bid parcel
     */
    public static function canMerchantCreateBidParcel($merchantId)
    {
        try {
            $settings = BiddingSetting::getSettings();
            
            // Check if bidding is globally enabled
            if (!($settings['bidding_enabled'] ?? false)) {
                return [
                    'can_create' => false,
                    'reason' => 'Bidding system is currently disabled'
                ];
            }

            // Check merchant's bid parcel limit
            $maxBidParcels = $settings['max_bid_parcels_per_merchant_per_day'] ?? 20;
            
            $todayBidParcels = Parcel::where('merchant_id', $merchantId)
                ->where('is_bid_parcel', true)
                ->whereDate('created_at', Carbon::today())
                ->count();

            if ($todayBidParcels >= $maxBidParcels) {
                return [
                    'can_create' => false,
                    'reason' => "Daily bid parcel limit reached ({$maxBidParcels})"
                ];
            }

            return [
                'can_create' => true,
                'reason' => 'Merchant can create bid parcel'
            ];

        } catch (\Exception $e) {
            Log::error('Failed to validate merchant bid parcel creation: ' . $e->getMessage());
            return [
                'can_create' => false,
                'reason' => 'Validation error occurred'
            ];
        }
    }

    /**
     * Validate bid amount
     */
    public static function validateBidAmount(Parcel $parcel, $bidAmount)
    {
        return BiddingCalculationHelper::validateBidAmount($parcel, $bidAmount);
    }

    /**
     * Validate bid timing
     */
    public static function validateBidTiming(Parcel $parcel, $estimatedPickupTime, $estimatedDeliveryTime)
    {
        try {
            $pickupTime = Carbon::parse($estimatedPickupTime);
            $deliveryTime = Carbon::parse($estimatedDeliveryTime);
            $now = Carbon::now();

            // Check if pickup time is in the future
            if ($pickupTime->lt($now)) {
                return [
                    'valid' => false,
                    'reason' => 'Estimated pickup time must be in the future'
                ];
            }

            // Check if delivery time is after pickup time
            if ($deliveryTime->lte($pickupTime)) {
                return [
                    'valid' => false,
                    'reason' => 'Estimated delivery time must be after pickup time'
                ];
            }

            // Check if timing is reasonable (not too far in the future)
            $maxFutureDays = BiddingSetting::getSettings()['max_future_pickup_days'] ?? 7;
            if ($pickupTime->gt($now->copy()->addDays($maxFutureDays))) {
                return [
                    'valid' => false,
                    'reason' => "Pickup time cannot be more than {$maxFutureDays} days in the future"
                ];
            }

            // Check minimum delivery time based on distance/type
            $minDeliveryHours = self::calculateMinimumDeliveryTime($parcel);
            if ($deliveryTime->lt($pickupTime->copy()->addHours($minDeliveryHours))) {
                return [
                    'valid' => false,
                    'reason' => "Minimum delivery time is {$minDeliveryHours} hours after pickup"
                ];
            }

            return [
                'valid' => true,
                'reason' => 'Timing is valid'
            ];

        } catch (\Exception $e) {
            Log::error('Failed to validate bid timing: ' . $e->getMessage());
            return [
                'valid' => false,
                'reason' => 'Timing validation error'
            ];
        }
    }

    /**
     * Calculate minimum delivery time based on parcel characteristics
     */
    private static function calculateMinimumDeliveryTime(Parcel $parcel)
    {
        try {
            $distance = $parcel->distance ?? 10;
            
            // Base minimum time
            $minHours = 2;
            
            // Add time based on distance
            if ($distance > 50) {
                $minHours = 24; // Next day for long distance
            } elseif ($distance > 20) {
                $minHours = 8; // Same day for medium distance
            } elseif ($distance > 10) {
                $minHours = 4; // Half day for short-medium distance
            }
            
            // Add time for special handling
            if ($parcel->fragile_liquid) {
                $minHours += 2; // Extra time for fragile items
            }
            
            return $minHours;

        } catch (\Exception $e) {
            Log::error('Failed to calculate minimum delivery time: ' . $e->getMessage());
            return 2; // Default minimum
        }
    }

    /**
     * Validate if a bid can be withdrawn
     */
    public static function canWithdrawBid(CompanyBid $bid)
    {
        try {
            // Check if bid is still pending
            if ($bid->status !== 'pending') {
                return [
                    'can_withdraw' => false,
                    'reason' => 'Only pending bids can be withdrawn'
                ];
            }

            // Check if parcel bidding is still open
            if ($bid->parcel->bid_status !== 'open') {
                return [
                    'can_withdraw' => false,
                    'reason' => 'Cannot withdraw bid after bidding is closed'
                ];
            }

            // Check withdrawal time limit
            $settings = BiddingSetting::getSettings();
            $withdrawalTimeLimit = $settings['bid_withdrawal_time_limit_minutes'] ?? 30;
            
            $bidAge = Carbon::now()->diffInMinutes($bid->created_at);
            if ($bidAge > $withdrawalTimeLimit) {
                return [
                    'can_withdraw' => false,
                    'reason' => "Bids can only be withdrawn within {$withdrawalTimeLimit} minutes of placement"
                ];
            }

            return [
                'can_withdraw' => true,
                'reason' => 'Bid can be withdrawn'
            ];

        } catch (\Exception $e) {
            Log::error('Failed to validate bid withdrawal: ' . $e->getMessage());
            return [
                'can_withdraw' => false,
                'reason' => 'Withdrawal validation error'
            ];
        }
    }

    /**
     * Validate if a merchant can accept a bid
     */
    public static function canAcceptBid(CompanyBid $bid, $merchantId)
    {
        try {
            $parcel = $bid->parcel;

            // Check if merchant owns the parcel
            if ($parcel->merchant_id !== $merchantId) {
                return [
                    'can_accept' => false,
                    'reason' => 'You can only accept bids on your own parcels'
                ];
            }

            // Check if bid is still pending
            if ($bid->status !== 'pending') {
                return [
                    'can_accept' => false,
                    'reason' => 'Only pending bids can be accepted'
                ];
            }

            // Check if parcel bidding is still open
            if ($parcel->bid_status !== 'open') {
                return [
                    'can_accept' => false,
                    'reason' => 'Bidding is already closed for this parcel'
                ];
            }

            // Check if company is still eligible
            $companyEligibility = self::canCompanyBid($parcel, $bid->company_id);
            if (!$companyEligibility['can_bid'] && $companyEligibility['reason'] !== 'Company has already placed a bid on this parcel') {
                return [
                    'can_accept' => false,
                    'reason' => 'Company is no longer eligible: ' . $companyEligibility['reason']
                ];
            }

            return [
                'can_accept' => true,
                'reason' => 'Bid can be accepted'
            ];

        } catch (\Exception $e) {
            Log::error('Failed to validate bid acceptance: ' . $e->getMessage());
            return [
                'can_accept' => false,
                'reason' => 'Acceptance validation error'
            ];
        }
    }

    /**
     * Validate hub bidding settings
     */
    public static function validateHubBiddingSettings($hubId)
    {
        try {
            $hub = Hub::find($hubId);
            
            if (!$hub) {
                return [
                    'valid' => false,
                    'reason' => 'Hub not found'
                ];
            }

            // Check if hub has bidding enabled companies
            $biddingCompanies = GeneralSettings::where('hub_id', $hubId)
                ->where('bidding_enabled', true)
                ->count();

            if ($biddingCompanies < 2) {
                return [
                    'valid' => false,
                    'reason' => 'Hub must have at least 2 companies with bidding enabled'
                ];
            }

            return [
                'valid' => true,
                'reason' => 'Hub bidding settings are valid'
            ];

        } catch (\Exception $e) {
            Log::error('Failed to validate hub bidding settings: ' . $e->getMessage());
            return [
                'valid' => false,
                'reason' => 'Hub validation error'
            ];
        }
    }

    /**
     * Get validation summary for a parcel
     */
    public static function getParcelBiddingValidationSummary(Parcel $parcel)
    {
        try {
            $summary = [
                'parcel_id' => $parcel->id,
                'tracking_id' => $parcel->tracking_id,
                'is_bid_parcel' => $parcel->is_bid_parcel,
                'bid_status' => $parcel->bid_status,
                'validations' => []
            ];

            // Hub validation
            $hubValidation = self::validateHubBiddingSettings($parcel->hub_id);
            $summary['validations']['hub'] = $hubValidation;

            // Timing validation
            $timingValid = !$parcel->bid_timeout_at || Carbon::now()->lt($parcel->bid_timeout_at);
            $summary['validations']['timing'] = [
                'valid' => $timingValid,
                'reason' => $timingValid ? 'Bidding time is valid' : 'Bidding time has expired'
            ];

            // Get eligible companies
            $eligibleCompanies = GeneralSettings::where('hub_id', $parcel->hub_id)
                ->where('bidding_enabled', true)
                ->get();

            $summary['eligible_companies'] = $eligibleCompanies->count();
            $summary['companies_with_bids'] = $parcel->companyBids()->where('status', 'pending')->count();

            return $summary;

        } catch (\Exception $e) {
            Log::error('Failed to get parcel bidding validation summary: ' . $e->getMessage());
            return [
                'error' => 'Failed to generate validation summary'
            ];
        }
    }
}
