# Delivery Man Frontend API Calls - Bidding System

## Overview
This document outlines all API endpoints that delivery man frontend applications (mobile app) need to integrate for handling bid parcels and assignments.

## Base Configuration
```javascript
const API_BASE_URL = 'https://your-domain.com/api/v10';
const API_HEADERS = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Authorization': 'Bearer ' + localStorage.getItem('deliveryman_auth_token'),
    'X-API-KEY': 'your-api-key'
};
```

## 1. Assigned Bid Parcels APIs

### 1.1 Get Assigned Bid Parcels
**Endpoint:** `GET /deliveryman/bidding/assigned-parcels`
**Purpose:** Get list of bid parcels assigned to the delivery man

```javascript
const getAssignedBidParcels = async (filters = {}) => {
    const queryParams = new URLSearchParams({
        page: filters.page || 1,
        per_page: filters.perPage || 20,
        status: filters.status || 'all', // pending, pickup_assigned, in_transit, delivered
        date_from: filters.dateFrom || '',
        date_to: filters.dateTo || '',
        sort_by: filters.sortBy || 'created_at',
        sort_order: filters.sortOrder || 'desc'
    });
    
    const response = await fetch(
        `${API_BASE_URL}/deliveryman/bidding/assigned-parcels?${queryParams}`,
        {
            method: 'GET',
            headers: API_HEADERS
        }
    );
    
    return await response.json();
};

// React Native component for assigned parcels
const AssignedBidParcels = () => {
    const [parcels, setParcels] = useState([]);
    const [statistics, setStatistics] = useState({});
    const [refreshing, setRefreshing] = useState(false);
    const [filters, setFilters] = useState({ status: 'all' });
    
    useEffect(() => {
        loadAssignedParcels();
        
        // Set up auto-refresh every 30 seconds
        const interval = setInterval(loadAssignedParcels, 30000);
        return () => clearInterval(interval);
    }, [filters]);
    
    const loadAssignedParcels = async () => {
        try {
            const result = await getAssignedBidParcels(filters);
            if (result.success) {
                setParcels(result.data.parcels);
                setStatistics(result.data.statistics);
            }
        } catch (error) {
            showErrorMessage('Failed to load assigned parcels');
        }
    };
    
    const onRefresh = async () => {
        setRefreshing(true);
        await loadAssignedParcels();
        setRefreshing(false);
    };
    
    return (
        <ScrollView 
            refreshControl={
                <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
            }
        >
            <View style={styles.container}>
                {/* Statistics Cards */}
                <View style={styles.statsContainer}>
                    <StatCard title="Total Assigned" value={statistics.total_assigned} />
                    <StatCard title="In Progress" value={statistics.in_progress} />
                    <StatCard title="Delivered" value={statistics.delivered} />
                </View>
                
                {/* Filter Tabs */}
                <View style={styles.filterTabs}>
                    <TouchableOpacity 
                        style={[styles.tab, filters.status === 'all' && styles.activeTab]}
                        onPress={() => setFilters({...filters, status: 'all'})}
                    >
                        <Text>All</Text>
                    </TouchableOpacity>
                    <TouchableOpacity 
                        style={[styles.tab, filters.status === 'pending' && styles.activeTab]}
                        onPress={() => setFilters({...filters, status: 'pending'})}
                    >
                        <Text>Pending</Text>
                    </TouchableOpacity>
                    <TouchableOpacity 
                        style={[styles.tab, filters.status === 'in_progress' && styles.activeTab]}
                        onPress={() => setFilters({...filters, status: 'in_progress'})}
                    >
                        <Text>In Progress</Text>
                    </TouchableOpacity>
                </View>
                
                {/* Parcel List */}
                <FlatList
                    data={parcels}
                    keyExtractor={(item) => item.id.toString()}
                    renderItem={({ item }) => (
                        <BidParcelCard 
                            parcel={item} 
                            onStatusUpdate={loadAssignedParcels}
                        />
                    )}
                />
            </View>
        </ScrollView>
    );
};

// Bid parcel card component
const BidParcelCard = ({ parcel, onStatusUpdate }) => {
    const getStatusColor = (status) => {
        const colors = {
            'pending': '#FFA500',
            'pickup_assigned': '#2196F3',
            'in_transit': '#FF9800',
            'delivered': '#4CAF50'
        };
        return colors[status] || '#757575';
    };
    
    return (
        <TouchableOpacity 
            style={styles.parcelCard}
            onPress={() => navigation.navigate('ParcelDetails', { parcelId: parcel.id })}
        >
            <View style={styles.cardHeader}>
                <Text style={styles.trackingId}>#{parcel.tracking_id}</Text>
                <View style={[styles.statusBadge, { backgroundColor: getStatusColor(parcel.status) }]}>
                    <Text style={styles.statusText}>{parcel.status}</Text>
                </View>
            </View>
            
            <View style={styles.cardContent}>
                <Text style={styles.merchantName}>{parcel.merchant.name}</Text>
                <Text style={styles.companyName}>Company: {parcel.company.name}</Text>
                <Text style={styles.address}>From: {parcel.pickup_address}</Text>
                <Text style={styles.address}>To: {parcel.customer_address}</Text>
                <Text style={styles.amount}>Amount: ${parcel.accepted_delivery_charge}</Text>
            </View>
            
            <View style={styles.cardActions}>
                {parcel.status === 'pickup_assigned' && (
                    <Button 
                        title="Start Pickup" 
                        onPress={() => updateParcelStatus(parcel.id, 'pickup_in_progress')}
                    />
                )}
                {parcel.status === 'pickup_in_progress' && (
                    <Button 
                        title="Mark In Transit" 
                        onPress={() => updateParcelStatus(parcel.id, 'in_transit')}
                    />
                )}
                {parcel.status === 'in_transit' && (
                    <Button 
                        title="Mark Delivered" 
                        onPress={() => showDeliveryConfirmation(parcel)}
                    />
                )}
            </View>
        </TouchableOpacity>
    );
};
```

### 1.2 Get Parcel Details
**Endpoint:** `GET /parcels/{id}` (with delivery man context)
**Purpose:** Get detailed information about an assigned bid parcel

```javascript
const getBidParcelDetails = async (parcelId) => {
    const response = await fetch(
        `${API_BASE_URL}/parcels/${parcelId}?include_bid_info=true&include_company_info=true`,
        {
            method: 'GET',
            headers: API_HEADERS
        }
    );
    
    return await response.json();
};

// Parcel details screen
const ParcelDetailsScreen = ({ route }) => {
    const { parcelId } = route.params;
    const [parcel, setParcel] = useState(null);
    const [loading, setLoading] = useState(true);
    
    useEffect(() => {
        loadParcelDetails();
    }, [parcelId]);
    
    const loadParcelDetails = async () => {
        try {
            const result = await getBidParcelDetails(parcelId);
            if (result.success) {
                setParcel(result.data);
            }
        } catch (error) {
            showErrorMessage('Failed to load parcel details');
        } finally {
            setLoading(false);
        }
    };
    
    if (loading) {
        return <LoadingSpinner />;
    }
    
    return (
        <ScrollView style={styles.container}>
            <View style={styles.header}>
                <Text style={styles.title}>Parcel #{parcel.tracking_id}</Text>
                <StatusBadge status={parcel.status} />
            </View>
            
            {/* Bid Information */}
            <Card title="Bid Information">
                <InfoRow label="Winning Company" value={parcel.company.name} />
                <InfoRow label="Bid Amount" value={`$${parcel.accepted_delivery_charge}`} />
                <InfoRow label="Original Offer" value={`$${parcel.offered_delivery_charge}`} />
                <InfoRow label="Savings" value={`$${parcel.offered_delivery_charge - parcel.accepted_delivery_charge}`} />
            </Card>
            
            {/* Pickup Information */}
            <Card title="Pickup Details">
                <InfoRow label="Address" value={parcel.pickup_address} />
                <InfoRow label="Phone" value={parcel.pickup_phone} />
                <InfoRow label="Merchant" value={parcel.merchant.name} />
                <InfoRow label="Contact" value={parcel.merchant.phone} />
            </Card>
            
            {/* Delivery Information */}
            <Card title="Delivery Details">
                <InfoRow label="Customer" value={parcel.customer_name} />
                <InfoRow label="Phone" value={parcel.customer_phone} />
                <InfoRow label="Address" value={parcel.customer_address} />
                <InfoRow label="COD Amount" value={`$${parcel.cod_amount}`} />
            </Card>
            
            {/* Action Buttons */}
            <View style={styles.actions}>
                <Button 
                    title="Call Merchant" 
                    onPress={() => makePhoneCall(parcel.pickup_phone)}
                />
                <Button 
                    title="Call Customer" 
                    onPress={() => makePhoneCall(parcel.customer_phone)}
                />
                <Button 
                    title="Navigate to Pickup" 
                    onPress={() => openMaps(parcel.pickup_lat, parcel.pickup_lng)}
                />
                <Button 
                    title="Navigate to Customer" 
                    onPress={() => openMaps(parcel.customer_lat, parcel.customer_lng)}
                />
            </View>
        </ScrollView>
    );
};
```

## 2. Status Update APIs

### 2.1 Update Parcel Status
**Endpoint:** `POST /deliveryman/bidding/parcel/{id}/update-status`
**Purpose:** Update the status of a bid parcel during delivery

```javascript
const updateParcelStatus = async (parcelId, status, additionalData = {}) => {
    const response = await fetch(
        `${API_BASE_URL}/deliveryman/bidding/parcel/${parcelId}/update-status`,
        {
            method: 'POST',
            headers: API_HEADERS,
            body: JSON.stringify({
                status: status,
                note: additionalData.note || '',
                delivery_proof: additionalData.deliveryProof || '',
                customer_signature: additionalData.customerSignature || '',
                delivery_lat: additionalData.latitude || null,
                delivery_lng: additionalData.longitude || null
            })
        }
    );
    
    return await response.json();
};

// Status update with location and proof
const handleStatusUpdate = async (parcelId, newStatus) => {
    try {
        // Get current location
        const location = await getCurrentLocation();
        
        let additionalData = {
            latitude: location.latitude,
            longitude: location.longitude
        };
        
        // If marking as delivered, collect proof
        if (newStatus === 'delivered') {
            const deliveryData = await showDeliveryConfirmation();
            additionalData = { ...additionalData, ...deliveryData };
        }
        
        const result = await updateParcelStatus(parcelId, newStatus, additionalData);
        if (result.success) {
            showSuccessMessage('Status updated successfully');
            // Refresh parcel list
            onStatusUpdate();
        } else {
            showErrorMessage(result.message);
        }
    } catch (error) {
        showErrorMessage('Failed to update status');
    }
};

// Delivery confirmation modal
const showDeliveryConfirmation = () => {
    return new Promise((resolve) => {
        Alert.alert(
            'Confirm Delivery',
            'Please confirm the delivery details',
            [
                {
                    text: 'Take Photo',
                    onPress: async () => {
                        const photo = await takePhoto();
                        const signature = await getCustomerSignature();
                        resolve({
                            deliveryProof: photo,
                            customerSignature: signature,
                            note: 'Delivered successfully'
                        });
                    }
                },
                {
                    text: 'No Photo',
                    onPress: () => {
                        resolve({
                            note: 'Delivered without photo proof'
                        });
                    }
                }
            ]
        );
    });
};
```

### 2.2 Update Location
**Endpoint:** `POST /deliveryman/bidding/location/update`
**Purpose:** Update delivery man's current location for tracking

```javascript
const updateLocation = async (latitude, longitude) => {
    const response = await fetch(
        `${API_BASE_URL}/deliveryman/bidding/location/update`,
        {
            method: 'POST',
            headers: API_HEADERS,
            body: JSON.stringify({
                latitude: latitude,
                longitude: longitude
            })
        }
    );
    
    return await response.json();
};

// Location tracking service
const startLocationTracking = () => {
    const watchId = navigator.geolocation.watchPosition(
        async (position) => {
            const { latitude, longitude } = position.coords;
            
            try {
                await updateLocation(latitude, longitude);
            } catch (error) {
                console.error('Failed to update location:', error);
            }
        },
        (error) => {
            console.error('Location error:', error);
        },
        {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 30000
        }
    );
    
    return watchId;
};

// React Native location tracking
const LocationTracker = () => {
    useEffect(() => {
        let watchId;
        
        const startTracking = async () => {
            const permission = await requestLocationPermission();
            if (permission) {
                watchId = Geolocation.watchPosition(
                    async (position) => {
                        const { latitude, longitude } = position.coords;
                        await updateLocation(latitude, longitude);
                    },
                    (error) => console.error('Location error:', error),
                    { enableHighAccuracy: true, distanceFilter: 10 }
                );
            }
        };
        
        startTracking();
        
        return () => {
            if (watchId) {
                Geolocation.clearWatch(watchId);
            }
        };
    }, []);
    
    return null; // This is a service component
};
```

## 3. Performance and Analytics APIs

### 3.1 Get Bid Performance Metrics
**Endpoint:** `GET /deliveryman/bidding/performance-metrics`
**Purpose:** Get delivery man's performance metrics for bid parcels

```javascript
const getBidPerformanceMetrics = async (days = 30) => {
    const response = await fetch(
        `${API_BASE_URL}/deliveryman/bidding/performance-metrics?days=${days}`,
        {
            method: 'GET',
            headers: API_HEADERS
        }
    );
    
    return await response.json();
};

// Performance dashboard
const PerformanceDashboard = () => {
    const [metrics, setMetrics] = useState(null);
    const [period, setPeriod] = useState(30);
    
    useEffect(() => {
        loadMetrics();
    }, [period]);
    
    const loadMetrics = async () => {
        const result = await getBidPerformanceMetrics(period);
        if (result.success) {
            setMetrics(result.data);
        }
    };
    
    return (
        <ScrollView style={styles.container}>
            <View style={styles.header}>
                <Text style={styles.title}>Bid Parcel Performance</Text>
                <Picker
                    selectedValue={period}
                    onValueChange={(value) => setPeriod(value)}
                >
                    <Picker.Item label="Last 7 days" value={7} />
                    <Picker.Item label="Last 30 days" value={30} />
                    <Picker.Item label="Last 90 days" value={90} />
                </Picker>
            </View>
            
            <View style={styles.metricsGrid}>
                <MetricCard 
                    title="Total Bid Parcels" 
                    value={metrics?.total_bid_parcels} 
                    icon="package"
                />
                <MetricCard 
                    title="Delivered Parcels" 
                    value={metrics?.delivered_parcels} 
                    icon="check-circle"
                />
                <MetricCard 
                    title="Success Rate" 
                    value={`${metrics?.delivery_success_rate}%`} 
                    icon="trending-up"
                />
                <MetricCard 
                    title="Total Earnings" 
                    value={`$${metrics?.total_earnings}`} 
                    icon="dollar-sign"
                />
                <MetricCard 
                    title="Avg Earnings/Parcel" 
                    value={`$${metrics?.average_earnings_per_parcel}`} 
                    icon="bar-chart"
                />
                <MetricCard 
                    title="Avg Delivery Time" 
                    value={`${metrics?.average_delivery_time_hours}h`} 
                    icon="clock"
                />
            </View>
            
            <View style={styles.ratingSection}>
                <Text style={styles.sectionTitle}>Current Rating</Text>
                <StarRating rating={metrics?.current_rating} />
            </View>
        </ScrollView>
    );
};

const MetricCard = ({ title, value, icon }) => (
    <View style={styles.metricCard}>
        <Icon name={icon} size={24} color="#2196F3" />
        <Text style={styles.metricTitle}>{title}</Text>
        <Text style={styles.metricValue}>{value}</Text>
    </View>
);
```

## 4. Utility Functions

```javascript
// Get current location
const getCurrentLocation = () => {
    return new Promise((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(
            (position) => {
                resolve({
                    latitude: position.coords.latitude,
                    longitude: position.coords.longitude
                });
            },
            (error) => reject(error),
            { enableHighAccuracy: true, timeout: 10000 }
        );
    });
};

// Take photo for delivery proof
const takePhoto = async () => {
    const options = {
        mediaType: 'photo',
        quality: 0.8,
        maxWidth: 1024,
        maxHeight: 1024
    };
    
    return new Promise((resolve) => {
        ImagePicker.launchCamera(options, (response) => {
            if (response.assets && response.assets[0]) {
                resolve(response.assets[0].base64);
            } else {
                resolve(null);
            }
        });
    });
};

// Get customer signature
const getCustomerSignature = async () => {
    // This would open a signature capture modal
    return new Promise((resolve) => {
        // Implementation depends on signature library used
        // Return base64 encoded signature
        resolve('signature_base64_data');
    });
};

// Make phone call
const makePhoneCall = (phoneNumber) => {
    const url = `tel:${phoneNumber}`;
    Linking.canOpenURL(url)
        .then((supported) => {
            if (supported) {
                return Linking.openURL(url);
            } else {
                Alert.alert('Error', 'Phone calls are not supported on this device');
            }
        })
        .catch((error) => console.error('Phone call error:', error));
};

// Open maps for navigation
const openMaps = (latitude, longitude) => {
    const url = Platform.select({
        ios: `maps:${latitude},${longitude}`,
        android: `geo:${latitude},${longitude}`
    });
    
    Linking.canOpenURL(url)
        .then((supported) => {
            if (supported) {
                return Linking.openURL(url);
            } else {
                // Fallback to Google Maps web
                const webUrl = `https://www.google.com/maps?q=${latitude},${longitude}`;
                return Linking.openURL(webUrl);
            }
        })
        .catch((error) => console.error('Maps error:', error));
};

// Request location permission (React Native)
const requestLocationPermission = async () => {
    if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
    }
    return true; // iOS handles permissions automatically
};
```

## 5. Error Handling and Offline Support

```javascript
// Offline queue for status updates
class OfflineQueue {
    constructor() {
        this.queue = [];
        this.isOnline = navigator.onLine;
        
        window.addEventListener('online', this.processQueue.bind(this));
        window.addEventListener('offline', () => { this.isOnline = false; });
    }
    
    addToQueue(action, data) {
        this.queue.push({ action, data, timestamp: Date.now() });
        
        if (this.isOnline) {
            this.processQueue();
        }
    }
    
    async processQueue() {
        this.isOnline = true;
        
        while (this.queue.length > 0) {
            const item = this.queue.shift();
            
            try {
                switch (item.action) {
                    case 'updateStatus':
                        await updateParcelStatus(item.data.parcelId, item.data.status, item.data.additionalData);
                        break;
                    case 'updateLocation':
                        await updateLocation(item.data.latitude, item.data.longitude);
                        break;
                }
            } catch (error) {
                // Re-add to queue if failed
                this.queue.unshift(item);
                break;
            }
        }
    }
}

const offlineQueue = new OfflineQueue();

// Enhanced status update with offline support
const updateParcelStatusOffline = async (parcelId, status, additionalData = {}) => {
    if (navigator.onLine) {
        return await updateParcelStatus(parcelId, status, additionalData);
    } else {
        offlineQueue.addToQueue('updateStatus', { parcelId, status, additionalData });
        showInfoMessage('Update queued for when connection is restored');
        return { success: true, queued: true };
    }
};
```

This completes the delivery man frontend API documentation. The next section will cover admin/hub frontend APIs.
