<?php

namespace App\Helpers\Bidding;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class BiddingSettingsHelper
{
    /**
     * Cache duration for settings (in minutes)
     */
    const CACHE_DURATION = 60;

    /**
     * Get a bidding setting value
     */
    public static function get($key, $default = null)
    {
        $cacheKey = "bidding_setting_{$key}";
        
        return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($key, $default) {
            $setting = DB::table('settings')->where('key', $key)->first();
            
            if (!$setting) {
                return $default;
            }
            
            // Cast value based on type
            return self::castValue($setting->value, $setting->type ?? 'string');
        });
    }

    /**
     * Set a bidding setting value
     */
    public static function set($key, $value, $type = 'string')
    {
        $exists = DB::table('settings')->where('key', $key)->exists();
        
        if ($exists) {
            DB::table('settings')
                ->where('key', $key)
                ->update([
                    'value' => $value,
                    'type' => $type,
                    'updated_at' => now()
                ]);
        } else {
            DB::table('settings')->insert([
                'key' => $key,
                'value' => $value,
                'type' => $type,
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }
        
        // Clear cache
        Cache::forget("bidding_setting_{$key}");
        
        return true;
    }

    /**
     * Get all bidding settings
     */
    public static function getAll()
    {
        return Cache::remember('all_bidding_settings', self::CACHE_DURATION, function () {
            $settings = DB::table('settings')
                ->where('key', 'LIKE', 'bidding_%')
                ->orWhere('key', 'LIKE', 'bid_%')
                ->orWhere('key', 'LIKE', '%_bid_%')
                ->get();
            
            $result = [];
            foreach ($settings as $setting) {
                $result[$setting->key] = self::castValue($setting->value, $setting->type ?? 'string');
            }
            
            return $result;
        });
    }

    /**
     * Check if bidding is enabled globally
     */
    public static function isBiddingEnabled()
    {
        return self::get('bidding_enabled', true);
    }

    /**
     * Get bid timeout in hours
     */
    public static function getBidTimeoutHours()
    {
        return self::get('bid_timeout_hours', 24);
    }

    /**
     * Get minimum companies required per hub
     */
    public static function getMinCompaniesPerHub()
    {
        return self::get('min_companies_per_hub', 2);
    }

    /**
     * Check if auto-accept best bid is enabled
     */
    public static function isAutoAcceptEnabled()
    {
        return self::get('auto_accept_best_bid', false);
    }

    /**
     * Check if bid notifications are enabled
     */
    public static function areBidNotificationsEnabled()
    {
        return self::get('bid_notification_enabled', true);
    }

    /**
     * Get company bid limit per parcel
     */
    public static function getCompanyBidLimit()
    {
        return self::get('company_bid_limit_per_parcel', 1);
    }

    /**
     * Get hub bidding commission percentage
     */
    public static function getHubCommission()
    {
        return self::get('hub_bidding_commission', 5.0);
    }

    /**
     * Get bidding percentage constraints
     */
    public static function getBiddingPercentageConstraints()
    {
        return [
            'min_percentage' => self::get('bidding_min_percentage', 50),
            'max_percentage' => self::get('bidding_max_percentage', 200)
        ];
    }

    /**
     * Get default bid amount constraints
     */
    public static function getDefaultBidConstraints()
    {
        return [
            'minimum_amount' => self::get('default_minimum_bid_amount', 10.0),
            'maximum_amount' => self::get('default_maximum_bid_amount', 1000.0)
        ];
    }

    /**
     * Get notification settings
     */
    public static function getNotificationSettings()
    {
        return [
            'deadline_reminder_hours' => self::get('bid_deadline_reminder_hours', 2),
            'new_bid_delay_minutes' => self::get('new_bid_notification_delay_minutes', 5),
            'retry_attempts' => self::get('bid_notification_retry_attempts', 3),
            'firebase_enabled' => self::get('firebase_bidding_enabled', true),
            'sms_enabled' => self::get('sms_bidding_notifications', false),
            'email_enabled' => self::get('email_bidding_notifications', true)
        ];
    }

    /**
     * Get company default settings
     */
    public static function getCompanyDefaults()
    {
        return [
            'pickup_time' => self::get('default_company_pickup_time', 60),
            'min_bid' => self::get('default_company_min_bid', 5.0),
            'max_bid' => self::get('default_company_max_bid', 500.0),
            'commission_rate' => self::get('default_company_commission_rate', 10.0)
        ];
    }

    /**
     * Get performance settings
     */
    public static function getPerformanceSettings()
    {
        return [
            'cleanup_days' => self::get('bid_cleanup_days', 30),
            'max_active_bids' => self::get('max_active_bids_per_company', 50),
            'search_radius_km' => self::get('bid_search_radius_km', 50),
            'rate_limit_per_hour' => self::get('bid_rate_limit_per_hour', 20)
        ];
    }

    /**
     * Get security settings
     */
    public static function getSecuritySettings()
    {
        return [
            'require_verification' => self::get('require_bid_verification', false),
            'high_value_threshold' => self::get('high_value_bid_threshold', 1000.0),
            'rate_limit' => self::get('bid_rate_limit_per_hour', 20)
        ];
    }

    /**
     * Check if feature is enabled
     */
    public static function isFeatureEnabled($feature)
    {
        $featureMap = [
            'suggestions' => 'enable_bid_suggestions',
            'auto_bidding' => 'enable_auto_bidding',
            'analytics' => 'enable_bid_analytics',
            'priority' => 'enable_priority_bidding'
        ];

        $settingKey = $featureMap[$feature] ?? "enable_{$feature}";
        return self::get($settingKey, false);
    }

    /**
     * Validate bid amount against constraints
     */
    public static function validateBidAmount($bidAmount, $offeredAmount)
    {
        $constraints = self::getBiddingPercentageConstraints();
        $defaults = self::getDefaultBidConstraints();

        // Check absolute minimum and maximum
        if ($bidAmount < $defaults['minimum_amount']) {
            return [
                'valid' => false,
                'message' => "Bid amount must be at least {$defaults['minimum_amount']}"
            ];
        }

        if ($bidAmount > $defaults['maximum_amount']) {
            return [
                'valid' => false,
                'message' => "Bid amount cannot exceed {$defaults['maximum_amount']}"
            ];
        }

        // Check percentage constraints
        $minAmount = $offeredAmount * ($constraints['min_percentage'] / 100);
        $maxAmount = $offeredAmount * ($constraints['max_percentage'] / 100);

        if ($bidAmount < $minAmount) {
            return [
                'valid' => false,
                'message' => "Bid amount must be at least {$constraints['min_percentage']}% of offered amount ({$minAmount})"
            ];
        }

        if ($bidAmount > $maxAmount) {
            return [
                'valid' => false,
                'message' => "Bid amount cannot exceed {$constraints['max_percentage']}% of offered amount ({$maxAmount})"
            ];
        }

        return [
            'valid' => true,
            'message' => 'Bid amount is valid'
        ];
    }

    /**
     * Clear all bidding settings cache
     */
    public static function clearCache()
    {
        $settings = DB::table('settings')
            ->where('key', 'LIKE', 'bidding_%')
            ->orWhere('key', 'LIKE', 'bid_%')
            ->orWhere('key', 'LIKE', '%_bid_%')
            ->pluck('key');

        foreach ($settings as $key) {
            Cache::forget("bidding_setting_{$key}");
        }

        Cache::forget('all_bidding_settings');
    }

    /**
     * Cast value to appropriate type
     */
    private static function castValue($value, $type)
    {
        switch ($type) {
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'integer':
                return (int) $value;
            case 'decimal':
            case 'float':
                return (float) $value;
            case 'array':
            case 'json':
                return json_decode($value, true) ?: [];
            default:
                return $value;
        }
    }

    /**
     * Update multiple settings at once
     */
    public static function updateMultiple(array $settings)
    {
        foreach ($settings as $key => $data) {
            $value = $data['value'] ?? $data;
            $type = $data['type'] ?? 'string';
            self::set($key, $value, $type);
        }

        return true;
    }

    /**
     * Reset settings to defaults
     */
    public static function resetToDefaults()
    {
        // This would re-run the seeder
        \Artisan::call('db:seed', ['--class' => 'BiddingSettingsSeeder']);
        self::clearCache();
    }
}
