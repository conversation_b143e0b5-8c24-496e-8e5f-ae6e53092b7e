<?php

namespace App\Http\Requests\Bidding;

use Illuminate\Foundation\Http\FormRequest;
use App\Helpers\Bidding\BiddingSettingsHelper;

class CreateBidParcelRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true; // Authorization handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $constraints = BiddingSettingsHelper::getDefaultBidConstraints();
        $timeoutHours = BiddingSettingsHelper::getBidTimeoutHours();
        
        return [
            // Basic parcel fields (inherit from normal parcel creation)
            'merchant_shop_id' => 'required|exists:merchant_shops,id',
            'pickup_address' => 'required|string|max:500',
            'pickup_phone' => 'required|string|max:20',
            'customer_name' => 'required|string|max:100',
            'customer_phone' => 'required|string|max:20',
            'customer_address' => 'required|string|max:500',
            'customer_lat' => 'nullable|numeric|between:-90,90',
            'customer_long' => 'nullable|numeric|between:-180,180',
            'invoice_no' => 'nullable|string|max:50',
            'category_id' => 'required|exists:delivery_categories,id',
            'weight' => 'required|numeric|min:0.1|max:1000',
            'delivery_type_id' => 'required|exists:delivery_types,id',
            'packaging_id' => 'nullable|exists:packagings,id',
            'cash_collection' => 'required|numeric|min:0',
            'selling_price' => 'nullable|numeric|min:0',
            'note' => 'nullable|string|max:1000',
            
            // Bidding specific fields
            'is_bid_parcel' => 'required|boolean|accepted',
            'offered_delivery_charge' => 'required|numeric|min:1',
            'minimum_bid_amount' => "nullable|numeric|min:{$constraints['minimum_amount']}|max:{$constraints['maximum_amount']}",
            'maximum_bid_amount' => "nullable|numeric|min:{$constraints['minimum_amount']}|max:{$constraints['maximum_amount']}|gte:minimum_bid_amount",
            'bid_requirements' => 'nullable|string|max:1000',
            'estimated_pickup_time' => 'nullable|integer|min:15|max:1440', // 15 minutes to 24 hours
            'bid_timeout_hours' => "nullable|integer|min:1|max:{$timeoutHours}",
            'priority_delivery' => 'nullable|boolean',
            'insurance_required' => 'nullable|boolean',
            'special_instructions' => 'nullable|string|max:500',
            
            // Hub and location
            'hub_id' => 'nullable|exists:hubs,id',
            'pickup_lat' => 'nullable|numeric|between:-90,90',
            'pickup_long' => 'nullable|numeric|between:-180,180'
        ];
    }

    /**
     * Get custom validation messages
     */
    public function messages()
    {
        return [
            'is_bid_parcel.accepted' => 'This request must be marked as a bid parcel',
            'offered_delivery_charge.required' => 'Offered delivery charge is required for bid parcels',
            'minimum_bid_amount.min' => 'Minimum bid amount is too low',
            'maximum_bid_amount.gte' => 'Maximum bid amount must be greater than or equal to minimum bid amount',
            'estimated_pickup_time.min' => 'Estimated pickup time must be at least 15 minutes',
            'estimated_pickup_time.max' => 'Estimated pickup time cannot exceed 24 hours',
            'bid_timeout_hours.max' => 'Bid timeout cannot exceed system maximum'
        ];
    }

    /**
     * Configure the validator instance
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Validate bid amount constraints
            if ($this->has('offered_delivery_charge') && $this->has('minimum_bid_amount')) {
                $validation = BiddingSettingsHelper::validateBidAmount(
                    $this->minimum_bid_amount,
                    $this->offered_delivery_charge
                );
                
                if (!$validation['valid']) {
                    $validator->errors()->add('minimum_bid_amount', $validation['message']);
                }
            }
            
            if ($this->has('offered_delivery_charge') && $this->has('maximum_bid_amount')) {
                $validation = BiddingSettingsHelper::validateBidAmount(
                    $this->maximum_bid_amount,
                    $this->offered_delivery_charge
                );
                
                if (!$validation['valid']) {
                    $validator->errors()->add('maximum_bid_amount', $validation['message']);
                }
            }
            
            // Validate hub has minimum companies
            if ($this->has('hub_id')) {
                $minCompanies = BiddingSettingsHelper::getMinCompaniesPerHub();
                $hubCompanies = \App\Models\Backend\GeneralSettings::where('hub_id', $this->hub_id)
                    ->where('bidding_enabled', true)
                    ->count();
                    
                if ($hubCompanies < $minCompanies) {
                    $validator->errors()->add('hub_id', 
                        "Selected hub must have at least {$minCompanies} companies with bidding enabled");
                }
            }
        });
    }

    /**
     * Prepare the data for validation
     */
    protected function prepareForValidation()
    {
        // Set default values if not provided
        if (!$this->has('minimum_bid_amount') && $this->has('offered_delivery_charge')) {
            $constraints = BiddingSettingsHelper::getBiddingPercentageConstraints();
            $minAmount = $this->offered_delivery_charge * ($constraints['min_percentage'] / 100);
            $this->merge(['minimum_bid_amount' => $minAmount]);
        }
        
        if (!$this->has('maximum_bid_amount') && $this->has('offered_delivery_charge')) {
            $constraints = BiddingSettingsHelper::getBiddingPercentageConstraints();
            $maxAmount = $this->offered_delivery_charge * ($constraints['max_percentage'] / 100);
            $this->merge(['maximum_bid_amount' => $maxAmount]);
        }
        
        if (!$this->has('bid_timeout_hours')) {
            $this->merge(['bid_timeout_hours' => BiddingSettingsHelper::getBidTimeoutHours()]);
        }
    }
}
