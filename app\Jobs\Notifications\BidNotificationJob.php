<?php

namespace App\Jobs\Notifications;

use App\Models\Backend\BidNotification;
use App\Services\BiddingNotificationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class BidNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $notificationType;
    protected $data;
    protected $retryCount;

    /**
     * Create a new job instance.
     */
    public function __construct(string $notificationType, array $data, int $retryCount = 0)
    {
        $this->notificationType = $notificationType;
        $this->data = $data;
        $this->retryCount = $retryCount;
    }

    /**
     * Execute the job.
     */
    public function handle(BiddingNotificationService $notificationService)
    {
        try {
            switch ($this->notificationType) {
                case 'new_bid_available':
                    $this->handleNewBidAvailable($notificationService);
                    break;
                    
                case 'bid_placed':
                    $this->handleBidPlaced($notificationService);
                    break;
                    
                case 'bid_accepted':
                    $this->handleBidAccepted($notificationService);
                    break;
                    
                case 'bidding_closed':
                    $this->handleBiddingClosed($notificationService);
                    break;
                    
                case 'bid_expired':
                    $this->handleBidExpired($notificationService);
                    break;
                    
                case 'deliveryman_assigned':
                    $this->handleDeliverymanAssigned($notificationService);
                    break;
                    
                default:
                    Log::warning("Unknown bid notification type: {$this->notificationType}");
            }
        } catch (\Exception $e) {
            Log::error("Bid notification job failed: " . $e->getMessage(), [
                'type' => $this->notificationType,
                'data' => $this->data,
                'retry_count' => $this->retryCount
            ]);
            
            // Retry logic
            if ($this->retryCount < 3) {
                dispatch(new self($this->notificationType, $this->data, $this->retryCount + 1))
                    ->delay(now()->addMinutes(5 * ($this->retryCount + 1)));
            }
            
            throw $e;
        }
    }

    private function handleNewBidAvailable(BiddingNotificationService $service)
    {
        $parcel = \App\Models\Backend\Parcel::find($this->data['parcel_id']);
        if ($parcel) {
            $companies = isset($this->data['company_ids']) 
                ? \App\Models\Backend\GeneralSettings::whereIn('id', $this->data['company_ids'])->get()
                : null;
            $service->notifyNewBidAvailable($parcel, $companies);
        }
    }

    private function handleBidPlaced(BiddingNotificationService $service)
    {
        $bid = \App\Models\Backend\CompanyBid::find($this->data['bid_id']);
        if ($bid) {
            $service->notifyBidPlaced($bid);
        }
    }

    private function handleBidAccepted(BiddingNotificationService $service)
    {
        $bid = \App\Models\Backend\CompanyBid::find($this->data['bid_id']);
        if ($bid) {
            $service->notifyBidAccepted($bid);
        }
    }

    private function handleBiddingClosed(BiddingNotificationService $service)
    {
        $parcel = \App\Models\Backend\Parcel::find($this->data['parcel_id']);
        if ($parcel) {
            $excludeCompanyId = $this->data['exclude_company_id'] ?? null;
            $service->notifyBiddingClosed($parcel, $excludeCompanyId);
        }
    }

    private function handleBidExpired(BiddingNotificationService $service)
    {
        $parcel = \App\Models\Backend\Parcel::find($this->data['parcel_id']);
        if ($parcel) {
            $service->notifyBidExpired($parcel);
        }
    }

    private function handleDeliverymanAssigned(BiddingNotificationService $service)
    {
        $parcel = \App\Models\Backend\Parcel::find($this->data['parcel_id']);
        $deliveryman = \App\Models\Backend\DeliveryMan::find($this->data['deliveryman_id']);
        
        if ($parcel && $deliveryman) {
            $service->notifyDeliverymanAssigned($parcel, $deliveryman);
        }
    }

    /**
     * The job failed to process.
     */
    public function failed(\Throwable $exception)
    {
        Log::error("Bid notification job permanently failed: " . $exception->getMessage(), [
            'type' => $this->notificationType,
            'data' => $this->data,
            'retry_count' => $this->retryCount
        ]);

        // Update notification record if it exists
        if (isset($this->data['notification_id'])) {
            BidNotification::where('id', $this->data['notification_id'])
                ->update([
                    'error_message' => $exception->getMessage(),
                    'retry_count' => $this->retryCount
                ]);
        }
    }
}
