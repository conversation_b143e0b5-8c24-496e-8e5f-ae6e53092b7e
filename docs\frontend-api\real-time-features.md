# Real-time Features - Bidding System

## Overview
This document outlines the real-time features required for the WeCourier bidding system, including WebSocket connections, push notifications, and live updates.

## Real-time Requirements

### 1. Merchant Real-time Features
- **New bid notifications**: Instant alerts when companies place bids
- **Bid updates**: Real-time bid amount changes and withdrawals
- **Bidding deadline alerts**: Countdown timers and expiration warnings
- **Bid acceptance confirmations**: Immediate feedback when bids are accepted

### 2. Company Real-time Features
- **New bid opportunities**: Instant notifications for new parcels open for bidding
- **Bid status updates**: Real-time feedback on bid acceptance/rejection
- **Competition updates**: Live updates on competing bids (anonymized)
- **Assignment notifications**: Alerts when parcels are assigned after winning bids

### 3. Delivery Man Real-time Features
- **Assignment notifications**: Instant alerts for new parcel assignments
- **Status update confirmations**: Real-time feedback on status changes
- **Route updates**: Live navigation and delivery instructions
- **Emergency alerts**: Urgent notifications from dispatch

### 4. Admin/Hub Real-time Features
- **System monitoring**: Live dashboard updates for bidding activity
- **Alert notifications**: System issues and threshold breaches
- **Performance metrics**: Real-time statistics and analytics updates

## WebSocket Implementation

### Connection Architecture
```javascript
// WebSocket connection structure
const WS_ENDPOINTS = {
    merchant: '/ws/merchant/{merchantId}',
    company: '/ws/company/{companyId}',
    deliveryman: '/ws/deliveryman/{deliverymanId}',
    admin: '/ws/admin/{adminId}',
    hub: '/ws/hub/{hubId}'
};

// Message types
const MESSAGE_TYPES = {
    // Bidding events
    NEW_BID_OPPORTUNITY: 'new_bid_opportunity',
    NEW_BID_PLACED: 'new_bid_placed',
    BID_ACCEPTED: 'bid_accepted',
    BID_REJECTED: 'bid_rejected',
    BID_WITHDRAWN: 'bid_withdrawn',
    BIDDING_CLOSED: 'bidding_closed',
    BIDDING_EXPIRED: 'bidding_expired',
    
    // Parcel events
    PARCEL_ASSIGNED: 'parcel_assigned',
    PARCEL_STATUS_UPDATED: 'parcel_status_updated',
    DELIVERY_COMPLETED: 'delivery_completed',
    
    // System events
    SYSTEM_ALERT: 'system_alert',
    MAINTENANCE_MODE: 'maintenance_mode',
    
    // Connection events
    HEARTBEAT: 'heartbeat',
    AUTHENTICATION: 'authentication',
    SUBSCRIPTION: 'subscription'
};
```

### WebSocket Service Implementation
```javascript
// services/websocketService.js
class WebSocketService {
    constructor() {
        this.connections = new Map();
        this.subscriptions = new Map();
        this.reconnectAttempts = new Map();
        this.maxReconnectAttempts = 5;
        this.heartbeatInterval = 30000; // 30 seconds
    }

    connect(userType, userId, channels = []) {
        const connectionId = `${userType}_${userId}`;
        
        if (this.connections.has(connectionId)) {
            this.disconnect(connectionId);
        }

        const wsUrl = `${process.env.REACT_APP_WS_URL}/ws/${userType}/${userId}`;
        const ws = new WebSocket(wsUrl);

        const connection = {
            ws,
            userType,
            userId,
            channels: new Set(channels),
            lastHeartbeat: Date.now(),
            isAuthenticated: false
        };

        this.setupConnectionHandlers(connectionId, connection);
        this.connections.set(connectionId, connection);
        
        return connectionId;
    }

    setupConnectionHandlers(connectionId, connection) {
        const { ws } = connection;

        ws.onopen = () => {
            console.log(`WebSocket connected: ${connectionId}`);
            this.authenticate(connectionId);
            this.startHeartbeat(connectionId);
            this.reconnectAttempts.delete(connectionId);
        };

        ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handleMessage(connectionId, data);
        };

        ws.onclose = (event) => {
            console.log(`WebSocket disconnected: ${connectionId}`, event.code);
            this.stopHeartbeat(connectionId);
            this.attemptReconnect(connectionId, connection);
        };

        ws.onerror = (error) => {
            console.error(`WebSocket error: ${connectionId}`, error);
            this.emit(connectionId, 'error', error);
        };
    }

    authenticate(connectionId) {
        const token = localStorage.getItem('auth_token');
        this.send(connectionId, {
            type: MESSAGE_TYPES.AUTHENTICATION,
            token
        });
    }

    subscribe(connectionId, channels) {
        const connection = this.connections.get(connectionId);
        if (!connection) return;

        channels.forEach(channel => connection.channels.add(channel));
        
        this.send(connectionId, {
            type: MESSAGE_TYPES.SUBSCRIPTION,
            channels: Array.from(connection.channels)
        });
    }

    handleMessage(connectionId, data) {
        const connection = this.connections.get(connectionId);
        if (!connection) return;

        switch (data.type) {
            case MESSAGE_TYPES.HEARTBEAT:
                connection.lastHeartbeat = Date.now();
                break;
                
            case MESSAGE_TYPES.AUTHENTICATION:
                connection.isAuthenticated = data.success;
                if (data.success) {
                    this.emit(connectionId, 'authenticated');
                } else {
                    this.emit(connectionId, 'authentication_failed', data.error);
                }
                break;
                
            default:
                this.emit(connectionId, data.type, data.payload);
                break;
        }
    }

    send(connectionId, data) {
        const connection = this.connections.get(connectionId);
        if (connection && connection.ws.readyState === WebSocket.OPEN) {
            connection.ws.send(JSON.stringify(data));
        }
    }

    startHeartbeat(connectionId) {
        const heartbeatId = setInterval(() => {
            const connection = this.connections.get(connectionId);
            if (!connection) {
                clearInterval(heartbeatId);
                return;
            }

            // Check if connection is still alive
            if (Date.now() - connection.lastHeartbeat > this.heartbeatInterval * 2) {
                console.warn(`Heartbeat timeout for ${connectionId}`);
                this.disconnect(connectionId);
                return;
            }

            this.send(connectionId, { type: MESSAGE_TYPES.HEARTBEAT });
        }, this.heartbeatInterval);

        const connection = this.connections.get(connectionId);
        if (connection) {
            connection.heartbeatId = heartbeatId;
        }
    }

    stopHeartbeat(connectionId) {
        const connection = this.connections.get(connectionId);
        if (connection && connection.heartbeatId) {
            clearInterval(connection.heartbeatId);
            delete connection.heartbeatId;
        }
    }

    attemptReconnect(connectionId, connection) {
        const attempts = this.reconnectAttempts.get(connectionId) || 0;
        
        if (attempts >= this.maxReconnectAttempts) {
            console.error(`Max reconnection attempts reached for ${connectionId}`);
            this.emit(connectionId, 'max_reconnect_attempts');
            return;
        }

        const delay = Math.pow(2, attempts) * 1000; // Exponential backoff
        
        setTimeout(() => {
            console.log(`Reconnecting ${connectionId}, attempt ${attempts + 1}`);
            this.reconnectAttempts.set(connectionId, attempts + 1);
            this.connect(connection.userType, connection.userId, Array.from(connection.channels));
        }, delay);
    }

    on(connectionId, event, callback) {
        const key = `${connectionId}_${event}`;
        if (!this.subscriptions.has(key)) {
            this.subscriptions.set(key, []);
        }
        this.subscriptions.get(key).push(callback);
    }

    off(connectionId, event, callback) {
        const key = `${connectionId}_${event}`;
        if (this.subscriptions.has(key)) {
            const callbacks = this.subscriptions.get(key);
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        }
    }

    emit(connectionId, event, data) {
        const key = `${connectionId}_${event}`;
        if (this.subscriptions.has(key)) {
            this.subscriptions.get(key).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error('Error in WebSocket callback:', error);
                }
            });
        }
    }

    disconnect(connectionId) {
        const connection = this.connections.get(connectionId);
        if (connection) {
            this.stopHeartbeat(connectionId);
            connection.ws.close();
            this.connections.delete(connectionId);
            
            // Clean up subscriptions
            for (const key of this.subscriptions.keys()) {
                if (key.startsWith(connectionId)) {
                    this.subscriptions.delete(key);
                }
            }
        }
    }

    disconnectAll() {
        for (const connectionId of this.connections.keys()) {
            this.disconnect(connectionId);
        }
    }
}

export default new WebSocketService();
```

### React Hook for Bidding Real-time Updates
```javascript
// hooks/useBiddingRealtime.js
import { useEffect, useRef, useCallback } from 'react';
import websocketService from '../services/websocketService';
import { useAuth } from './useAuth';
import { useBidding } from '../contexts/BiddingContext';

export const useBiddingRealtime = (options = {}) => {
    const { user } = useAuth();
    const { actions } = useBidding();
    const connectionIdRef = useRef(null);
    const optionsRef = useRef(options);

    useEffect(() => {
        optionsRef.current = options;
    }, [options]);

    const handleNewBidOpportunity = useCallback((parcel) => {
        // Add to available parcels
        actions.addAvailableParcel(parcel);
        
        // Show notification
        if (optionsRef.current.showNotifications) {
            showNotification(`New bid opportunity: ${parcel.tracking_id}`, {
                type: 'info',
                action: {
                    label: 'View',
                    onClick: () => optionsRef.current.onParcelClick?.(parcel)
                }
            });
        }

        optionsRef.current.onNewBidOpportunity?.(parcel);
    }, [actions]);

    const handleBidAccepted = useCallback((bid) => {
        actions.updateBidStatus(bid.id, 'accepted');
        
        if (optionsRef.current.showNotifications) {
            showNotification(`Your bid of $${bid.offered_charge} was accepted!`, {
                type: 'success'
            });
        }

        optionsRef.current.onBidAccepted?.(bid);
    }, [actions]);

    const handleBidRejected = useCallback((bid) => {
        actions.updateBidStatus(bid.id, 'rejected');
        
        if (optionsRef.current.showNotifications) {
            showNotification(`Your bid was not selected`, {
                type: 'warning'
            });
        }

        optionsRef.current.onBidRejected?.(bid);
    }, [actions]);

    const handleBiddingClosed = useCallback((parcel) => {
        actions.removeAvailableParcel(parcel.id);
        
        if (optionsRef.current.showNotifications) {
            showNotification(`Bidding closed for ${parcel.tracking_id}`, {
                type: 'info'
            });
        }

        optionsRef.current.onBiddingClosed?.(parcel);
    }, [actions]);

    useEffect(() => {
        if (!user) return;

        // Determine user type and channels
        const userType = user.type || 'merchant'; // merchant, company, deliveryman, admin
        const channels = ['bidding', 'parcels'];

        // Connect to WebSocket
        connectionIdRef.current = websocketService.connect(userType, user.id, channels);

        // Set up event listeners
        websocketService.on(connectionIdRef.current, 'authenticated', () => {
            console.log('WebSocket authenticated');
            optionsRef.current.onConnected?.();
        });

        websocketService.on(connectionIdRef.current, MESSAGE_TYPES.NEW_BID_OPPORTUNITY, handleNewBidOpportunity);
        websocketService.on(connectionIdRef.current, MESSAGE_TYPES.BID_ACCEPTED, handleBidAccepted);
        websocketService.on(connectionIdRef.current, MESSAGE_TYPES.BID_REJECTED, handleBidRejected);
        websocketService.on(connectionIdRef.current, MESSAGE_TYPES.BIDDING_CLOSED, handleBiddingClosed);

        websocketService.on(connectionIdRef.current, 'error', (error) => {
            console.error('WebSocket error:', error);
            optionsRef.current.onError?.(error);
        });

        // Cleanup on unmount
        return () => {
            if (connectionIdRef.current) {
                websocketService.disconnect(connectionIdRef.current);
            }
        };
    }, [user, handleNewBidOpportunity, handleBidAccepted, handleBidRejected, handleBiddingClosed]);

    return {
        isConnected: connectionIdRef.current && websocketService.connections.has(connectionIdRef.current),
        connectionId: connectionIdRef.current
    };
};
```

## Push Notifications

### Service Worker Setup
```javascript
// public/sw.js
self.addEventListener('push', function(event) {
    if (!event.data) return;

    const data = event.data.json();
    const options = {
        body: data.body,
        icon: '/icons/icon-192x192.png',
        badge: '/icons/badge-72x72.png',
        tag: data.tag || 'bidding-notification',
        data: data.data || {},
        actions: data.actions || [],
        requireInteraction: data.requireInteraction || false,
        silent: data.silent || false
    };

    event.waitUntil(
        self.registration.showNotification(data.title, options)
    );
});

self.addEventListener('notificationclick', function(event) {
    event.notification.close();

    if (event.action) {
        // Handle action clicks
        handleNotificationAction(event.action, event.notification.data);
    } else {
        // Handle notification click
        event.waitUntil(
            clients.openWindow(event.notification.data.url || '/')
        );
    }
});

function handleNotificationAction(action, data) {
    switch (action) {
        case 'view_parcel':
            clients.openWindow(`/parcels/${data.parcelId}`);
            break;
        case 'place_bid':
            clients.openWindow(`/bidding/parcel/${data.parcelId}`);
            break;
        case 'accept_bid':
            // Send message to open tabs
            clients.matchAll().then(clients => {
                clients.forEach(client => {
                    client.postMessage({
                        type: 'ACCEPT_BID',
                        bidId: data.bidId
                    });
                });
            });
            break;
    }
}
```

### Push Notification Service
```javascript
// services/pushNotificationService.js
class PushNotificationService {
    constructor() {
        this.registration = null;
        this.subscription = null;
    }

    async initialize() {
        if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
            console.warn('Push notifications not supported');
            return false;
        }

        try {
            this.registration = await navigator.serviceWorker.register('/sw.js');
            console.log('Service Worker registered');
            return true;
        } catch (error) {
            console.error('Service Worker registration failed:', error);
            return false;
        }
    }

    async requestPermission() {
        const permission = await Notification.requestPermission();
        return permission === 'granted';
    }

    async subscribe() {
        if (!this.registration) {
            await this.initialize();
        }

        try {
            this.subscription = await this.registration.pushManager.subscribe({
                userVisibleOnly: true,
                applicationServerKey: this.urlBase64ToUint8Array(process.env.REACT_APP_VAPID_PUBLIC_KEY)
            });

            // Send subscription to server
            await this.sendSubscriptionToServer(this.subscription);
            return this.subscription;
        } catch (error) {
            console.error('Push subscription failed:', error);
            return null;
        }
    }

    async sendSubscriptionToServer(subscription) {
        const response = await fetch('/api/v10/push-subscriptions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
            },
            body: JSON.stringify({
                subscription,
                user_type: 'company', // or merchant, deliveryman, admin
                topics: ['bidding', 'parcels', 'system']
            })
        });

        return response.json();
    }

    urlBase64ToUint8Array(base64String) {
        const padding = '='.repeat((4 - base64String.length % 4) % 4);
        const base64 = (base64String + padding)
            .replace(/-/g, '+')
            .replace(/_/g, '/');

        const rawData = window.atob(base64);
        const outputArray = new Uint8Array(rawData.length);

        for (let i = 0; i < rawData.length; ++i) {
            outputArray[i] = rawData.charCodeAt(i);
        }
        return outputArray;
    }

    async unsubscribe() {
        if (this.subscription) {
            await this.subscription.unsubscribe();
            this.subscription = null;
        }
    }
}

export default new PushNotificationService();
```

## Real-time Dashboard Components

### Live Bidding Dashboard
```javascript
// components/LiveBiddingDashboard.js
import React, { useState, useEffect } from 'react';
import { useBiddingRealtime } from '../hooks/useBiddingRealtime';

const LiveBiddingDashboard = () => {
    const [stats, setStats] = useState({
        activeParcels: 0,
        totalBids: 0,
        avgBidAmount: 0,
        recentActivity: []
    });

    const { isConnected } = useBiddingRealtime({
        showNotifications: true,
        onNewBidOpportunity: (parcel) => {
            setStats(prev => ({
                ...prev,
                activeParcels: prev.activeParcels + 1,
                recentActivity: [
                    { type: 'new_parcel', data: parcel, timestamp: Date.now() },
                    ...prev.recentActivity.slice(0, 9)
                ]
            }));
        },
        onBidAccepted: (bid) => {
            setStats(prev => ({
                ...prev,
                recentActivity: [
                    { type: 'bid_accepted', data: bid, timestamp: Date.now() },
                    ...prev.recentActivity.slice(0, 9)
                ]
            }));
        }
    });

    return (
        <div className="live-dashboard">
            <div className="connection-status">
                <span className={`indicator ${isConnected ? 'connected' : 'disconnected'}`}>
                    {isConnected ? 'Live' : 'Offline'}
                </span>
            </div>

            <div className="stats-grid">
                <StatCard title="Active Parcels" value={stats.activeParcels} />
                <StatCard title="Total Bids" value={stats.totalBids} />
                <StatCard title="Avg Bid Amount" value={`$${stats.avgBidAmount}`} />
            </div>

            <div className="activity-feed">
                <h3>Recent Activity</h3>
                {stats.recentActivity.map((activity, index) => (
                    <ActivityItem key={index} activity={activity} />
                ))}
            </div>
        </div>
    );
};
```

This completes the real-time features documentation. The final section will cover API testing documentation.
