# WeCourier Bidding System - API Integration Guide

## Overview
This guide provides comprehensive implementation examples and best practices for integrating the WeCourier bidding system APIs into frontend applications.

## Table of Contents
1. [Authentication & Setup](#authentication--setup)
2. [Error Handling](#error-handling)
3. [State Management](#state-management)
4. [Real-time Updates](#real-time-updates)
5. [Offline Support](#offline-support)
6. [Performance Optimization](#performance-optimization)
7. [Testing Strategies](#testing-strategies)
8. [Security Best Practices](#security-best-practices)

## Authentication & Setup

### API Configuration
```javascript
// config/api.js
export const API_CONFIG = {
    BASE_URL: process.env.REACT_APP_API_URL || 'https://api.wecourier.com/api/v10',
    TIMEOUT: 30000,
    RETRY_ATTEMPTS: 3,
    RETRY_DELAY: 1000
};

export const getAuthHeaders = () => ({
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
    'X-API-KEY': process.env.REACT_APP_API_KEY
});
```

### API Client Setup
```javascript
// services/apiClient.js
import axios from 'axios';
import { API_CONFIG, getAuthHeaders } from '../config/api';

class ApiClient {
    constructor() {
        this.client = axios.create({
            baseURL: API_CONFIG.BASE_URL,
            timeout: API_CONFIG.TIMEOUT,
            headers: getAuthHeaders()
        });

        this.setupInterceptors();
    }

    setupInterceptors() {
        // Request interceptor
        this.client.interceptors.request.use(
            (config) => {
                // Add auth token to every request
                config.headers = { ...config.headers, ...getAuthHeaders() };
                return config;
            },
            (error) => Promise.reject(error)
        );

        // Response interceptor
        this.client.interceptors.response.use(
            (response) => response.data,
            (error) => {
                if (error.response?.status === 401) {
                    // Handle token expiration
                    this.handleTokenExpiration();
                }
                return Promise.reject(this.formatError(error));
            }
        );
    }

    handleTokenExpiration() {
        localStorage.removeItem('auth_token');
        window.location.href = '/login';
    }

    formatError(error) {
        return {
            message: error.response?.data?.message || error.message,
            status: error.response?.status,
            errors: error.response?.data?.errors
        };
    }

    // HTTP methods
    get(url, config = {}) {
        return this.client.get(url, config);
    }

    post(url, data = {}, config = {}) {
        return this.client.post(url, data, config);
    }

    put(url, data = {}, config = {}) {
        return this.client.put(url, data, config);
    }

    delete(url, config = {}) {
        return this.client.delete(url, config);
    }
}

export default new ApiClient();
```

### Service Layer Pattern
```javascript
// services/biddingService.js
import apiClient from './apiClient';

export class BiddingService {
    // Merchant services
    async createBidParcel(parcelData) {
        return apiClient.post('/merchant/bidding/parcel/create', parcelData);
    }

    async getParcelBids(parcelId, filters = {}) {
        const params = new URLSearchParams(filters);
        return apiClient.get(`/merchant/bidding/parcel/${parcelId}/company-bids?${params}`);
    }

    async acceptCompanyBid(parcelId, bidId, reason = '') {
        return apiClient.post(`/merchant/bidding/parcel/${parcelId}/accept-company-bid`, {
            bid_id: bidId,
            acceptance_reason: reason
        });
    }

    // Company services
    async getAvailableParcels(filters = {}) {
        const params = new URLSearchParams(filters);
        return apiClient.get(`/company/bidding/available-parcels?${params}`);
    }

    async placeBid(parcelId, bidData) {
        return apiClient.post(`/company/bidding/parcel/${parcelId}/bid`, bidData);
    }

    async getMyBids(filters = {}) {
        const params = new URLSearchParams(filters);
        return apiClient.get(`/company/bidding/my-bids?${params}`);
    }

    // Delivery man services
    async getAssignedBidParcels(filters = {}) {
        const params = new URLSearchParams(filters);
        return apiClient.get(`/deliveryman/bidding/assigned-parcels?${params}`);
    }

    async updateParcelStatus(parcelId, status, additionalData = {}) {
        return apiClient.post(`/deliveryman/bidding/parcel/${parcelId}/update-status`, {
            status,
            ...additionalData
        });
    }

    // Admin services
    async getHubStatistics(hubId, days = 30) {
        return apiClient.get(`/hub/bidding/${hubId}/statistics?days=${days}`);
    }

    async toggleCompanyBidding(hubId, companyId, enabled) {
        return apiClient.post(`/hub/bidding/${hubId}/company/${companyId}/toggle-bidding`, {
            bidding_enabled: enabled
        });
    }
}

export default new BiddingService();
```

## Error Handling

### Centralized Error Handler
```javascript
// utils/errorHandler.js
export class ErrorHandler {
    static handle(error, context = '') {
        console.error(`Error in ${context}:`, error);

        // Log to external service
        this.logError(error, context);

        // Show user-friendly message
        this.showUserMessage(error);

        // Handle specific error types
        switch (error.status) {
            case 401:
                this.handleUnauthorized();
                break;
            case 403:
                this.handleForbidden(error);
                break;
            case 422:
                this.handleValidationError(error);
                break;
            case 429:
                this.handleRateLimit(error);
                break;
            default:
                this.handleGenericError(error);
        }
    }

    static logError(error, context) {
        // Send to logging service (e.g., Sentry, LogRocket)
        if (window.Sentry) {
            window.Sentry.captureException(error, {
                tags: { context },
                extra: { timestamp: new Date().toISOString() }
            });
        }
    }

    static showUserMessage(error) {
        const message = this.getUserFriendlyMessage(error);
        
        // Use your notification system
        if (window.showNotification) {
            window.showNotification(message, 'error');
        } else {
            alert(message);
        }
    }

    static getUserFriendlyMessage(error) {
        const messages = {
            401: 'Your session has expired. Please log in again.',
            403: 'You do not have permission to perform this action.',
            404: 'The requested resource was not found.',
            422: 'Please check your input and try again.',
            429: 'Too many requests. Please wait a moment and try again.',
            500: 'Server error. Please try again later.',
            503: 'Service temporarily unavailable. Please try again later.'
        };

        return messages[error.status] || error.message || 'An unexpected error occurred.';
    }

    static handleValidationError(error) {
        if (error.errors) {
            // Display field-specific errors
            Object.keys(error.errors).forEach(field => {
                const fieldErrors = error.errors[field];
                this.highlightFieldError(field, fieldErrors[0]);
            });
        }
    }

    static highlightFieldError(field, message) {
        const fieldElement = document.querySelector(`[name="${field}"]`);
        if (fieldElement) {
            fieldElement.classList.add('error');
            
            // Show error message near field
            const errorElement = document.createElement('div');
            errorElement.className = 'field-error';
            errorElement.textContent = message;
            fieldElement.parentNode.appendChild(errorElement);
        }
    }
}

// Usage in components
try {
    const result = await biddingService.placeBid(parcelId, bidData);
    // Handle success
} catch (error) {
    ErrorHandler.handle(error, 'placing bid');
}
```

## State Management

### Redux/Context Pattern
```javascript
// store/biddingSlice.js (Redux Toolkit)
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import biddingService from '../services/biddingService';

// Async thunks
export const fetchAvailableParcels = createAsyncThunk(
    'bidding/fetchAvailableParcels',
    async (filters, { rejectWithValue }) => {
        try {
            const response = await biddingService.getAvailableParcels(filters);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.message);
        }
    }
);

export const placeBid = createAsyncThunk(
    'bidding/placeBid',
    async ({ parcelId, bidData }, { rejectWithValue }) => {
        try {
            const response = await biddingService.placeBid(parcelId, bidData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.message);
        }
    }
);

const biddingSlice = createSlice({
    name: 'bidding',
    initialState: {
        availableParcels: [],
        myBids: [],
        loading: false,
        error: null,
        filters: {},
        pagination: {}
    },
    reducers: {
        setFilters: (state, action) => {
            state.filters = action.payload;
        },
        clearError: (state) => {
            state.error = null;
        },
        updateBidStatus: (state, action) => {
            const { bidId, status } = action.payload;
            const bid = state.myBids.find(b => b.id === bidId);
            if (bid) {
                bid.status = status;
            }
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchAvailableParcels.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchAvailableParcels.fulfilled, (state, action) => {
                state.loading = false;
                state.availableParcels = action.payload.parcels;
                state.pagination = action.payload.pagination;
            })
            .addCase(fetchAvailableParcels.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
            .addCase(placeBid.fulfilled, (state, action) => {
                state.myBids.push(action.payload.bid);
            });
    }
});

export const { setFilters, clearError, updateBidStatus } = biddingSlice.actions;
export default biddingSlice.reducer;
```

### React Context Pattern
```javascript
// contexts/BiddingContext.js
import React, { createContext, useContext, useReducer, useEffect } from 'react';
import biddingService from '../services/biddingService';

const BiddingContext = createContext();

const initialState = {
    availableParcels: [],
    myBids: [],
    loading: false,
    error: null
};

function biddingReducer(state, action) {
    switch (action.type) {
        case 'SET_LOADING':
            return { ...state, loading: action.payload };
        case 'SET_ERROR':
            return { ...state, error: action.payload, loading: false };
        case 'SET_AVAILABLE_PARCELS':
            return { ...state, availableParcels: action.payload, loading: false };
        case 'SET_MY_BIDS':
            return { ...state, myBids: action.payload, loading: false };
        case 'ADD_BID':
            return { ...state, myBids: [...state.myBids, action.payload] };
        case 'UPDATE_BID':
            return {
                ...state,
                myBids: state.myBids.map(bid =>
                    bid.id === action.payload.id ? action.payload : bid
                )
            };
        default:
            return state;
    }
}

export const BiddingProvider = ({ children }) => {
    const [state, dispatch] = useReducer(biddingReducer, initialState);

    const actions = {
        async fetchAvailableParcels(filters = {}) {
            dispatch({ type: 'SET_LOADING', payload: true });
            try {
                const response = await biddingService.getAvailableParcels(filters);
                dispatch({ type: 'SET_AVAILABLE_PARCELS', payload: response.data.parcels });
            } catch (error) {
                dispatch({ type: 'SET_ERROR', payload: error.message });
            }
        },

        async placeBid(parcelId, bidData) {
            try {
                const response = await biddingService.placeBid(parcelId, bidData);
                dispatch({ type: 'ADD_BID', payload: response.data.bid });
                return response;
            } catch (error) {
                dispatch({ type: 'SET_ERROR', payload: error.message });
                throw error;
            }
        }
    };

    return (
        <BiddingContext.Provider value={{ state, actions }}>
            {children}
        </BiddingContext.Provider>
    );
};

export const useBidding = () => {
    const context = useContext(BiddingContext);
    if (!context) {
        throw new Error('useBidding must be used within a BiddingProvider');
    }
    return context;
};
```

## Real-time Updates

### WebSocket Integration
```javascript
// services/websocketService.js
class WebSocketService {
    constructor() {
        this.ws = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.listeners = new Map();
    }

    connect(userId, userType) {
        const wsUrl = `${process.env.REACT_APP_WS_URL}/ws/${userType}/${userId}`;
        
        this.ws = new WebSocket(wsUrl);
        
        this.ws.onopen = () => {
            console.log('WebSocket connected');
            this.reconnectAttempts = 0;
            this.emit('connected');
        };

        this.ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handleMessage(data);
        };

        this.ws.onclose = () => {
            console.log('WebSocket disconnected');
            this.emit('disconnected');
            this.attemptReconnect(userId, userType);
        };

        this.ws.onerror = (error) => {
            console.error('WebSocket error:', error);
            this.emit('error', error);
        };
    }

    handleMessage(data) {
        switch (data.type) {
            case 'new_bid_opportunity':
                this.emit('newBidOpportunity', data.parcel);
                break;
            case 'bid_accepted':
                this.emit('bidAccepted', data.bid);
                break;
            case 'bid_rejected':
                this.emit('bidRejected', data.bid);
                break;
            case 'bidding_closed':
                this.emit('biddingClosed', data.parcel);
                break;
            case 'parcel_status_updated':
                this.emit('parcelStatusUpdated', data.parcel);
                break;
        }
    }

    attemptReconnect(userId, userType) {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            setTimeout(() => {
                this.reconnectAttempts++;
                console.log(`Reconnecting... Attempt ${this.reconnectAttempts}`);
                this.connect(userId, userType);
            }, this.reconnectDelay * Math.pow(2, this.reconnectAttempts));
        }
    }

    on(event, callback) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event).push(callback);
    }

    off(event, callback) {
        if (this.listeners.has(event)) {
            const callbacks = this.listeners.get(event);
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        }
    }

    emit(event, data) {
        if (this.listeners.has(event)) {
            this.listeners.get(event).forEach(callback => callback(data));
        }
    }

    disconnect() {
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
    }
}

export default new WebSocketService();
```

### React Hook for WebSocket
```javascript
// hooks/useWebSocket.js
import { useEffect, useRef } from 'react';
import websocketService from '../services/websocketService';

export const useWebSocket = (userId, userType, callbacks = {}) => {
    const callbacksRef = useRef(callbacks);
    
    useEffect(() => {
        callbacksRef.current = callbacks;
    }, [callbacks]);

    useEffect(() => {
        if (!userId || !userType) return;

        // Connect to WebSocket
        websocketService.connect(userId, userType);

        // Set up event listeners
        const eventHandlers = {
            newBidOpportunity: (data) => callbacksRef.current.onNewBidOpportunity?.(data),
            bidAccepted: (data) => callbacksRef.current.onBidAccepted?.(data),
            bidRejected: (data) => callbacksRef.current.onBidRejected?.(data),
            biddingClosed: (data) => callbacksRef.current.onBiddingClosed?.(data),
            parcelStatusUpdated: (data) => callbacksRef.current.onParcelStatusUpdated?.(data),
            connected: () => callbacksRef.current.onConnected?.(),
            disconnected: () => callbacksRef.current.onDisconnected?.(),
            error: (error) => callbacksRef.current.onError?.(error)
        };

        // Register event listeners
        Object.entries(eventHandlers).forEach(([event, handler]) => {
            websocketService.on(event, handler);
        });

        // Cleanup on unmount
        return () => {
            Object.entries(eventHandlers).forEach(([event, handler]) => {
                websocketService.off(event, handler);
            });
            websocketService.disconnect();
        };
    }, [userId, userType]);
};

// Usage in component
const BidOpportunities = () => {
    const [parcels, setParcels] = useState([]);
    const { user } = useAuth();

    useWebSocket(user.id, 'company', {
        onNewBidOpportunity: (parcel) => {
            setParcels(prev => [parcel, ...prev]);
            showNotification(`New bid opportunity: ${parcel.tracking_id}`);
        },
        onBiddingClosed: (parcel) => {
            setParcels(prev => prev.filter(p => p.id !== parcel.id));
        },
        onConnected: () => {
            console.log('Real-time updates connected');
        },
        onError: (error) => {
            console.error('WebSocket error:', error);
        }
    });

    return (
        <div>
            {/* Component content */}
        </div>
    );
};
```

## Performance Optimization

### Request Caching
```javascript
// utils/cache.js
class ApiCache {
    constructor(ttl = 5 * 60 * 1000) { // 5 minutes default TTL
        this.cache = new Map();
        this.ttl = ttl;
    }

    set(key, data) {
        this.cache.set(key, {
            data,
            timestamp: Date.now()
        });
    }

    get(key) {
        const item = this.cache.get(key);
        if (!item) return null;

        if (Date.now() - item.timestamp > this.ttl) {
            this.cache.delete(key);
            return null;
        }

        return item.data;
    }

    clear() {
        this.cache.clear();
    }

    delete(key) {
        this.cache.delete(key);
    }
}

export const apiCache = new ApiCache();

// Enhanced service with caching
export class CachedBiddingService extends BiddingService {
    async getAvailableParcels(filters = {}) {
        const cacheKey = `available-parcels-${JSON.stringify(filters)}`;
        const cached = apiCache.get(cacheKey);
        
        if (cached) {
            return cached;
        }

        const result = await super.getAvailableParcels(filters);
        apiCache.set(cacheKey, result);
        return result;
    }

    async placeBid(parcelId, bidData) {
        const result = await super.placeBid(parcelId, bidData);
        
        // Invalidate related cache entries
        this.invalidateParcelCaches(parcelId);
        
        return result;
    }

    invalidateParcelCaches(parcelId) {
        // Remove cached entries that might be affected
        for (const key of apiCache.cache.keys()) {
            if (key.includes('available-parcels') || key.includes(`parcel-${parcelId}`)) {
                apiCache.delete(key);
            }
        }
    }
}
```

### Request Debouncing
```javascript
// hooks/useDebounce.js
import { useState, useEffect } from 'react';

export const useDebounce = (value, delay) => {
    const [debouncedValue, setDebouncedValue] = useState(value);

    useEffect(() => {
        const handler = setTimeout(() => {
            setDebouncedValue(value);
        }, delay);

        return () => {
            clearTimeout(handler);
        };
    }, [value, delay]);

    return debouncedValue;
};

// Usage in search component
const ParcelSearch = () => {
    const [searchTerm, setSearchTerm] = useState('');
    const debouncedSearchTerm = useDebounce(searchTerm, 500);

    useEffect(() => {
        if (debouncedSearchTerm) {
            searchParcels(debouncedSearchTerm);
        }
    }, [debouncedSearchTerm]);

    return (
        <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search parcels..."
        />
    );
};
```

This completes the API integration guide. The next section will cover real-time features documentation.
