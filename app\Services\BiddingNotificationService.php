<?php

namespace App\Services;

use App\Models\Backend\BidNotification;
use App\Models\Backend\Parcel;
use App\Models\Backend\CompanyBid;
use App\Models\Backend\GeneralSettings;
use App\Jobs\Notifications\SendPushNotification;
use App\Jobs\Notifications\AndroidPushNotification;
use App\Jobs\NotifyViaMqtt;
use App\Base\Constants\Masters\PushEnums;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class BiddingNotificationService
{
    /**
     * Send notification when a new bid parcel is available
     */
    public function notifyNewBidAvailable(Parcel $parcel, $companies = null)
    {
        try {
            // Get eligible companies if not provided
            if (!$companies) {
                $companies = GeneralSettings::where('hub_id', $parcel->hub_id)
                    ->where('bidding_enabled', true)
                    ->get();
            }

            if ($companies->isEmpty()) {
                return false;
            }

            // Create bid notifications
            BidNotification::createForNewBid($parcel, $companies);

            // Send push notifications to each company
            foreach ($companies as $company) {
                $this->sendPushNotification($company, [
                    'title' => 'New Bid Available',
                    'message' => "New parcel available for bidding: {$parcel->tracking_id}",
                    'type' => 'new_bid_available',
                    'parcel_id' => $parcel->id,
                    'tracking_id' => $parcel->tracking_id,
                    'pickup_address' => $parcel->pickup_address,
                    'delivery_address' => $parcel->customer_address,
                    'offered_charge' => $parcel->offered_delivery_charge,
                    'bid_timeout' => $parcel->bid_timeout_at?->toISOString()
                ]);
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send new bid notifications: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Send notification when a bid is placed
     */
    public function notifyBidPlaced(CompanyBid $bid)
    {
        try {
            $parcel = $bid->parcel;
            $merchant = $parcel->merchant;

            // Create notification record
            BidNotification::create([
                'company_id' => null, // This is for merchant
                'parcel_id' => $parcel->id,
                'company_bid_id' => $bid->id,
                'notification_type' => 'bid_placed',
                'title' => 'New Bid Received',
                'message' => "New bid received for parcel {$parcel->tracking_id}",
                'data' => [
                    'bid_id' => $bid->id,
                    'company_name' => $bid->company->name,
                    'offered_charge' => $bid->offered_charge,
                    'estimated_pickup_time' => $bid->estimated_pickup_time,
                    'estimated_delivery_time' => $bid->estimated_delivery_time
                ]
            ]);

            // Send push notification to merchant
            if ($merchant && $merchant->user) {
                $this->sendPushNotificationToUser($merchant->user, [
                    'title' => 'New Bid Received',
                    'message' => "New bid of {$bid->offered_charge} received for parcel {$parcel->tracking_id}",
                    'type' => 'bid_placed',
                    'parcel_id' => $parcel->id,
                    'bid_id' => $bid->id,
                    'company_name' => $bid->company->name,
                    'offered_charge' => $bid->offered_charge
                ]);
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send bid placed notification: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Send notification when a bid is accepted
     */
    public function notifyBidAccepted(CompanyBid $bid)
    {
        try {
            $parcel = $bid->parcel;
            $company = $bid->company;

            // Create notification record
            BidNotification::create([
                'company_id' => $company->id,
                'parcel_id' => $parcel->id,
                'company_bid_id' => $bid->id,
                'notification_type' => 'bid_accepted',
                'title' => 'Bid Accepted',
                'message' => "Your bid for parcel {$parcel->tracking_id} has been accepted",
                'data' => [
                    'bid_id' => $bid->id,
                    'parcel_id' => $parcel->id,
                    'tracking_id' => $parcel->tracking_id,
                    'accepted_charge' => $bid->offered_charge
                ]
            ]);

            // Send push notification to company
            $this->sendPushNotification($company, [
                'title' => 'Bid Accepted!',
                'message' => "Congratulations! Your bid for parcel {$parcel->tracking_id} has been accepted",
                'type' => 'bid_accepted',
                'parcel_id' => $parcel->id,
                'bid_id' => $bid->id,
                'tracking_id' => $parcel->tracking_id,
                'accepted_charge' => $bid->offered_charge
            ]);

            // Notify other companies that bidding is closed
            $this->notifyBiddingClosed($parcel, $company->id);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send bid accepted notification: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Send notification when bidding is closed
     */
    public function notifyBiddingClosed(Parcel $parcel, $excludeCompanyId = null)
    {
        try {
            $companies = GeneralSettings::where('hub_id', $parcel->hub_id)
                ->where('bidding_enabled', true);

            if ($excludeCompanyId) {
                $companies->where('id', '!=', $excludeCompanyId);
            }

            $companies = $companies->get();

            foreach ($companies as $company) {
                // Create notification record
                BidNotification::create([
                    'company_id' => $company->id,
                    'parcel_id' => $parcel->id,
                    'notification_type' => 'bidding_closed',
                    'title' => 'Bidding Closed',
                    'message' => "Bidding for parcel {$parcel->tracking_id} has been closed",
                    'data' => [
                        'parcel_id' => $parcel->id,
                        'tracking_id' => $parcel->tracking_id
                    ]
                ]);

                // Send push notification
                $this->sendPushNotification($company, [
                    'title' => 'Bidding Closed',
                    'message' => "Bidding for parcel {$parcel->tracking_id} has been closed",
                    'type' => 'bidding_closed',
                    'parcel_id' => $parcel->id,
                    'tracking_id' => $parcel->tracking_id
                ]);
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send bidding closed notifications: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Send notification when bid expires
     */
    public function notifyBidExpired(Parcel $parcel)
    {
        try {
            $merchant = $parcel->merchant;

            // Send notification to merchant
            if ($merchant && $merchant->user) {
                $this->sendPushNotificationToUser($merchant->user, [
                    'title' => 'Bid Expired',
                    'message' => "Bidding time for parcel {$parcel->tracking_id} has expired",
                    'type' => 'bid_expired',
                    'parcel_id' => $parcel->id,
                    'tracking_id' => $parcel->tracking_id
                ]);
            }

            // Notify companies that had pending bids
            $pendingBids = $parcel->companyBids()->where('status', 'pending')->get();
            foreach ($pendingBids as $bid) {
                $this->sendPushNotification($bid->company, [
                    'title' => 'Bid Expired',
                    'message' => "Bidding time for parcel {$parcel->tracking_id} has expired",
                    'type' => 'bid_expired',
                    'parcel_id' => $parcel->id,
                    'bid_id' => $bid->id,
                    'tracking_id' => $parcel->tracking_id
                ]);
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send bid expired notifications: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Send notification when deliveryman is assigned
     */
    public function notifyDeliverymanAssigned(Parcel $parcel, $deliveryman)
    {
        try {
            $merchant = $parcel->merchant;

            // Notify merchant
            if ($merchant && $merchant->user) {
                $this->sendPushNotificationToUser($merchant->user, [
                    'title' => 'Deliveryman Assigned',
                    'message' => "Deliveryman {$deliveryman->user->name} assigned to parcel {$parcel->tracking_id}",
                    'type' => 'deliveryman_assigned',
                    'parcel_id' => $parcel->id,
                    'tracking_id' => $parcel->tracking_id,
                    'deliveryman_name' => $deliveryman->user->name,
                    'deliveryman_phone' => $deliveryman->user->mobile
                ]);
            }

            // Notify deliveryman
            if ($deliveryman && $deliveryman->user) {
                $this->sendPushNotificationToUser($deliveryman->user, [
                    'title' => 'New Bid Parcel Assigned',
                    'message' => "You have been assigned to deliver parcel {$parcel->tracking_id}",
                    'type' => 'bid_parcel_assigned',
                    'parcel_id' => $parcel->id,
                    'tracking_id' => $parcel->tracking_id,
                    'pickup_address' => $parcel->pickup_address,
                    'delivery_address' => $parcel->customer_address
                ]);
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send deliveryman assigned notifications: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Send push notification to company
     */
    private function sendPushNotification(GeneralSettings $company, array $data)
    {
        // Implementation depends on how company users are stored
        // This is a placeholder - adjust based on your user structure
        if ($company->notification_email) {
            // Send to company admin or designated notification recipient
            // You may need to adjust this based on your user model structure
        }
    }

    /**
     * Send push notification to specific user
     */
    private function sendPushNotificationToUser($user, array $data)
    {
        try {
            $title = $data['title'];
            $message = $data['message'];
            
            // Use existing WeCourier notification system
            dispatch(new SendPushNotification($user, $title, $message, $data));
            
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send push notification to user: ' . $e->getMessage());
            return false;
        }
    }
}
