<?php

namespace App\Http\Controllers\Api\V10;

use App\Http\Controllers\Controller;
use App\Models\Backend\Parcel;
use App\Models\Backend\CompanyBid;
use App\Models\Backend\GeneralSettings;
use App\Models\Backend\DeliveryMan;
use App\Services\HubBiddingService;
use App\Helpers\Bidding\CompanyBiddingHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class CompanyBiddingController extends Controller
{
    protected $hubBiddingService;

    public function __construct(HubBiddingService $hubBiddingService)
    {
        $this->hubBiddingService = $hubBiddingService;
    }

    /**
     * Get available parcels for bidding
     */
    public function getAvailableParcels(Request $request)
    {
        try {
            $company = $this->getCurrentCompany();
            if (!$company) {
                return response()->json([
                    'success' => false,
                    'message' => 'Company not found'
                ], 404);
            }

            if (!$company->bidding_enabled) {
                return response()->json([
                    'success' => false,
                    'message' => 'Bidding is disabled for your company'
                ], 403);
            }

            // Get parcels in the same hub that are open for bidding
            $parcels = Parcel::with(['merchant', 'category', 'deliveryType'])
                ->where('hub_id', $company->hub_id)
                ->where('is_bid_parcel', true)
                ->where('bid_status', 'open')
                ->where(function($query) {
                    $query->whereNull('bid_timeout_at')
                          ->orWhere('bid_timeout_at', '>', Carbon::now());
                })
                ->whereDoesntHave('companyBids', function($query) use ($company) {
                    $query->where('company_id', $company->id);
                })
                ->orderBy('created_at', 'desc')
                ->paginate(20);

            $parcelsData = $parcels->getCollection()->map(function($parcel) use ($company) {
                $bidSuggestion = CompanyBiddingHelper::suggestOptimalBidAmount($company->id, $parcel->id);
                
                return [
                    'id' => $parcel->id,
                    'tracking_id' => $parcel->tracking_id,
                    'pickup_address' => $parcel->pickup_address,
                    'customer_address' => $parcel->customer_address,
                    'weight' => $parcel->weight,
                    'category' => $parcel->category->title ?? null,
                    'delivery_type' => $parcel->deliveryType->title ?? null,
                    'offered_delivery_charge' => $parcel->offered_delivery_charge,
                    'minimum_bid_amount' => $parcel->minimum_bid_amount,
                    'maximum_bid_amount' => $parcel->maximum_bid_amount,
                    'bid_requirements' => $parcel->bid_requirements,
                    'estimated_pickup_time' => $parcel->estimated_pickup_time,
                    'bid_timeout_at' => $parcel->bid_timeout_at,
                    'active_bids_count' => $parcel->active_bids_count,
                    'lowest_bid' => $parcel->lowest_bid?->offered_charge,
                    'created_at' => $parcel->created_at,
                    'time_remaining_hours' => $parcel->bid_timeout_at ? 
                        Carbon::now()->diffInHours($parcel->bid_timeout_at, false) : null,
                    'suggested_bid' => $bidSuggestion
                ];
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'parcels' => $parcelsData,
                    'pagination' => [
                        'current_page' => $parcels->currentPage(),
                        'last_page' => $parcels->lastPage(),
                        'per_page' => $parcels->perPage(),
                        'total' => $parcels->total()
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get available parcels: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Place a bid on a parcel
     */
    public function placeBid(Request $request, $parcelId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'offered_charge' => 'required|numeric|min:1',
                'estimated_pickup_time' => 'nullable|integer|min:15|max:1440',
                'estimated_delivery_time' => 'nullable|integer|min:30|max:2880',
                'message' => 'nullable|string|max:500',
                'contact_person' => 'nullable|string|max:100',
                'contact_phone' => 'nullable|string|max:20',
                'contact_email' => 'nullable|email|max:100',
                'service_features' => 'nullable|array',
                'insurance_coverage' => 'nullable|numeric|min:0',
                'priority_delivery' => 'nullable|boolean',
                'terms_conditions' => 'nullable|string|max:1000'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $company = $this->getCurrentCompany();
            if (!$company) {
                return response()->json([
                    'success' => false,
                    'message' => 'Company not found'
                ], 404);
            }

            // Validate bid
            $validation = CompanyBiddingHelper::validateCompanyBid(
                $company->id, 
                $parcelId, 
                $request->offered_charge
            );

            if (!$validation['valid']) {
                return response()->json([
                    'success' => false,
                    'message' => $validation['message']
                ], 400);
            }

            // Prepare bid data
            $bidData = $request->only([
                'offered_charge', 'estimated_pickup_time', 'estimated_delivery_time',
                'message', 'contact_person', 'contact_phone', 'contact_email',
                'service_features', 'insurance_coverage', 'priority_delivery',
                'terms_conditions'
            ]);

            // Set defaults
            $bidData['estimated_pickup_time'] = $bidData['estimated_pickup_time'] ?? $company->default_pickup_time;
            $bidData['contact_person'] = $bidData['contact_person'] ?? $company->name;
            $bidData['contact_phone'] = $bidData['contact_phone'] ?? $company->phone;

            // Place bid through service
            $result = $this->hubBiddingService->processCompanyBid($company->id, $parcelId, $bidData);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => [
                        'bid' => $result['bid']
                    ]
                ], 201);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message']
                ], 400);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to place bid: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get company's bids
     */
    public function getMyBids(Request $request)
    {
        try {
            $company = $this->getCurrentCompany();
            if (!$company) {
                return response()->json([
                    'success' => false,
                    'message' => 'Company not found'
                ], 404);
            }

            $status = $request->get('status', 'all');
            $query = $company->companyBids()->with(['parcel.merchant']);

            if ($status !== 'all') {
                $query->where('status', $status);
            }

            $bids = $query->orderBy('created_at', 'desc')->paginate(20);

            $bidsData = $bids->getCollection()->map(function($bid) {
                return [
                    'id' => $bid->id,
                    'parcel' => [
                        'id' => $bid->parcel->id,
                        'tracking_id' => $bid->parcel->tracking_id,
                        'pickup_address' => $bid->parcel->pickup_address,
                        'customer_address' => $bid->parcel->customer_address,
                        'merchant_name' => $bid->parcel->merchant->business_name ?? 'N/A'
                    ],
                    'offered_charge' => $bid->offered_charge,
                    'estimated_pickup_time' => $bid->estimated_pickup_time,
                    'estimated_delivery_time' => $bid->estimated_delivery_time,
                    'status' => $bid->status,
                    'created_at' => $bid->created_at,
                    'accepted_at' => $bid->accepted_at,
                    'rejected_at' => $bid->rejected_at,
                    'can_withdraw' => $bid->canBeWithdrawn()
                ];
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'bids' => $bidsData,
                    'pagination' => [
                        'current_page' => $bids->currentPage(),
                        'last_page' => $bids->lastPage(),
                        'per_page' => $bids->perPage(),
                        'total' => $bids->total()
                    ],
                    'statistics' => [
                        'total_bids' => $company->companyBids()->count(),
                        'pending_bids' => $company->companyBids()->where('status', 'pending')->count(),
                        'won_bids' => $company->companyBids()->where('status', 'accepted')->count(),
                        'success_rate' => $company->getBiddingSuccessRate()
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get company bids: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Withdraw a bid
     */
    public function withdrawBid(Request $request, $bidId)
    {
        try {
            $company = $this->getCurrentCompany();
            $bid = CompanyBid::where('id', $bidId)
                            ->where('company_id', $company->id)
                            ->first();

            if (!$bid) {
                return response()->json([
                    'success' => false,
                    'message' => 'Bid not found'
                ], 404);
            }

            if (!$bid->canBeWithdrawn()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Bid cannot be withdrawn'
                ], 400);
            }

            $bid->withdraw();

            return response()->json([
                'success' => true,
                'message' => 'Bid withdrawn successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to withdraw bid: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Assign deliveryman to won parcel
     */
    public function assignDeliveryman(Request $request, $parcelId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'deliveryman_id' => 'required|exists:delivery_man,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $company = $this->getCurrentCompany();
            $parcel = Parcel::find($parcelId);

            if (!$parcel) {
                return response()->json([
                    'success' => false,
                    'message' => 'Parcel not found'
                ], 404);
            }

            if ($parcel->winning_company_id !== $company->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access to parcel'
                ], 403);
            }

            // Verify deliveryman belongs to company
            $deliveryman = DeliveryMan::where('id', $request->deliveryman_id)
                                     ->where('company_id', $company->id)
                                     ->where('status', 1)
                                     ->first();

            if (!$deliveryman) {
                return response()->json([
                    'success' => false,
                    'message' => 'Deliveryman not found or not available'
                ], 404);
            }

            // Assign deliveryman
            if ($parcel->assignDeliveryman($deliveryman->id)) {
                return response()->json([
                    'success' => true,
                    'message' => 'Deliveryman assigned successfully',
                    'data' => [
                        'parcel' => $parcel->fresh(),
                        'deliveryman' => [
                            'id' => $deliveryman->id,
                            'name' => $deliveryman->name,
                            'phone' => $deliveryman->phone
                        ]
                    ]
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to assign deliveryman'
                ], 400);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to assign deliveryman: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get current company from authenticated user
     */
    private function getCurrentCompany()
    {
        // Assuming the authenticated user has a company relationship
        // This might need adjustment based on your authentication structure
        return Auth::user()->company ?? GeneralSettings::first();
    }
}
