<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class BiddingSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $biddingSettings = [
            // Core bidding settings
            [
                'key' => 'bidding_enabled',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Enable/disable the bidding system globally'
            ],
            [
                'key' => 'bid_timeout_hours',
                'value' => '24',
                'type' => 'integer',
                'description' => 'Default timeout for bid parcels in hours'
            ],
            [
                'key' => 'min_companies_per_hub',
                'value' => '2',
                'type' => 'integer',
                'description' => 'Minimum number of companies required in a hub for bidding'
            ],
            [
                'key' => 'auto_accept_best_bid',
                'value' => '0',
                'type' => 'boolean',
                'description' => 'Automatically accept the best bid after timeout'
            ],
            [
                'key' => 'bid_notification_enabled',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Send notifications to companies about new bid opportunities'
            ],
            [
                'key' => 'company_bid_limit_per_parcel',
                'value' => '1',
                'type' => 'integer',
                'description' => 'Maximum number of bids a company can place per parcel'
            ],
            [
                'key' => 'hub_bidding_commission',
                'value' => '5.00',
                'type' => 'decimal',
                'description' => 'Hub commission percentage on successful bid parcels'
            ],
            
            // Bid amount constraints
            [
                'key' => 'bidding_min_percentage',
                'value' => '50',
                'type' => 'integer',
                'description' => 'Minimum bid percentage below offered delivery charge'
            ],
            [
                'key' => 'bidding_max_percentage',
                'value' => '200',
                'type' => 'integer',
                'description' => 'Maximum bid percentage above offered delivery charge'
            ],
            [
                'key' => 'default_minimum_bid_amount',
                'value' => '10.00',
                'type' => 'decimal',
                'description' => 'Default minimum bid amount for parcels'
            ],
            [
                'key' => 'default_maximum_bid_amount',
                'value' => '1000.00',
                'type' => 'decimal',
                'description' => 'Default maximum bid amount for parcels'
            ],
            
            // Notification settings
            [
                'key' => 'bid_deadline_reminder_hours',
                'value' => '2',
                'type' => 'integer',
                'description' => 'Hours before bid deadline to send reminder notifications'
            ],
            [
                'key' => 'new_bid_notification_delay_minutes',
                'value' => '5',
                'type' => 'integer',
                'description' => 'Delay in minutes before sending new bid notifications to avoid spam'
            ],
            [
                'key' => 'bid_notification_retry_attempts',
                'value' => '3',
                'type' => 'integer',
                'description' => 'Number of retry attempts for failed notifications'
            ],
            
            // Performance and optimization
            [
                'key' => 'bid_cleanup_days',
                'value' => '30',
                'type' => 'integer',
                'description' => 'Number of days to keep expired bid records before cleanup'
            ],
            [
                'key' => 'max_active_bids_per_company',
                'value' => '50',
                'type' => 'integer',
                'description' => 'Maximum number of active bids a company can have'
            ],
            [
                'key' => 'bid_search_radius_km',
                'value' => '50',
                'type' => 'integer',
                'description' => 'Search radius in kilometers for finding eligible companies'
            ],
            
            // Company defaults
            [
                'key' => 'default_company_pickup_time',
                'value' => '60',
                'type' => 'integer',
                'description' => 'Default pickup time in minutes for new companies'
            ],
            [
                'key' => 'default_company_min_bid',
                'value' => '5.00',
                'type' => 'decimal',
                'description' => 'Default minimum bid amount for new companies'
            ],
            [
                'key' => 'default_company_max_bid',
                'value' => '500.00',
                'type' => 'decimal',
                'description' => 'Default maximum bid amount for new companies'
            ],
            [
                'key' => 'default_company_commission_rate',
                'value' => '10.00',
                'type' => 'decimal',
                'description' => 'Default commission rate for new companies'
            ],
            
            // Advanced features
            [
                'key' => 'enable_bid_suggestions',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Enable AI-powered bid amount suggestions'
            ],
            [
                'key' => 'enable_auto_bidding',
                'value' => '0',
                'type' => 'boolean',
                'description' => 'Allow companies to enable automatic bidding'
            ],
            [
                'key' => 'enable_bid_analytics',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Enable detailed bidding analytics and reporting'
            ],
            [
                'key' => 'enable_priority_bidding',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Allow merchants to mark parcels as priority for faster bidding'
            ],
            
            // Integration settings
            [
                'key' => 'firebase_bidding_enabled',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Enable Firebase real-time notifications for bidding'
            ],
            [
                'key' => 'sms_bidding_notifications',
                'value' => '0',
                'type' => 'boolean',
                'description' => 'Enable SMS notifications for critical bidding events'
            ],
            [
                'key' => 'email_bidding_notifications',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Enable email notifications for bidding events'
            ],
            
            // Security and validation
            [
                'key' => 'bid_rate_limit_per_hour',
                'value' => '20',
                'type' => 'integer',
                'description' => 'Maximum number of bids a company can place per hour'
            ],
            [
                'key' => 'require_bid_verification',
                'value' => '0',
                'type' => 'boolean',
                'description' => 'Require manual verification for high-value bids'
            ],
            [
                'key' => 'high_value_bid_threshold',
                'value' => '1000.00',
                'type' => 'decimal',
                'description' => 'Threshold amount for high-value bids requiring verification'
            ]
        ];

        // Insert settings with timestamps
        foreach ($biddingSettings as &$setting) {
            $setting['created_at'] = now();
            $setting['updated_at'] = now();
        }

        // Check if settings table exists and has the required columns
        if (DB::getSchemaBuilder()->hasTable('settings')) {
            // Delete existing bidding settings to avoid duplicates
            DB::table('settings')->where('key', 'LIKE', 'bidding_%')->delete();
            DB::table('settings')->where('key', 'LIKE', 'bid_%')->delete();
            DB::table('settings')->where('key', 'LIKE', '%_bid_%')->delete();
            
            // Insert new settings
            DB::table('settings')->insert($biddingSettings);
            
            $this->command->info('Bidding settings seeded successfully!');
        } else {
            $this->command->warn('Settings table not found. Please run the settings migration first.');
        }
    }
}
