<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('parcels', function (Blueprint $table) {
            // Bidding system fields
            $table->boolean('is_bid_parcel')->default(false)->after('status');
            $table->decimal('offered_delivery_charge', 13, 2)->default(0)->after('is_bid_parcel');
            $table->decimal('accepted_delivery_charge', 13, 2)->default(0)->after('offered_delivery_charge');
            $table->timestamp('bid_timeout_at')->nullable()->after('accepted_delivery_charge');
            $table->foreignId('winning_company_id')->nullable()->constrained('general_settings')->onDelete('set null')->after('bid_timeout_at');
            $table->foreignId('assigned_deliveryman_id')->nullable()->constrained('delivery_man')->onDelete('set null')->after('winning_company_id');
            $table->enum('bid_status', ['open', 'closed', 'expired', 'assigned'])->default('open')->after('assigned_deliveryman_id');
            
            // Bidding metadata
            $table->text('bid_requirements')->nullable()->after('bid_status')->comment('Special requirements for the bid');
            $table->integer('estimated_pickup_time')->nullable()->after('bid_requirements')->comment('Estimated pickup time in minutes');
            $table->decimal('minimum_bid_amount', 13, 2)->nullable()->after('estimated_pickup_time');
            $table->decimal('maximum_bid_amount', 13, 2)->nullable()->after('minimum_bid_amount');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('parcels', function (Blueprint $table) {
            $table->dropForeign(['winning_company_id']);
            $table->dropForeign(['assigned_deliveryman_id']);
            $table->dropColumn([
                'is_bid_parcel',
                'offered_delivery_charge',
                'accepted_delivery_charge',
                'bid_timeout_at',
                'winning_company_id',
                'assigned_deliveryman_id',
                'bid_status',
                'bid_requirements',
                'estimated_pickup_time',
                'minimum_bid_amount',
                'maximum_bid_amount'
            ]);
        });
    }
};
