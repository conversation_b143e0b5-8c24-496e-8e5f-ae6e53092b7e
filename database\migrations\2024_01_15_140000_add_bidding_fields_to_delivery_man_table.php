<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('delivery_man', function (Blueprint $table) {
            // Location tracking for bidding
            $table->decimal('current_lat', 10, 8)->nullable()->after('delivery_long');
            $table->decimal('current_lng', 11, 8)->nullable()->after('current_lat');
            $table->timestamp('last_location_update')->nullable()->after('current_lng');
            
            // Bidding availability
            $table->boolean('available_for_bidding')->default(true)->after('status');
            $table->boolean('currently_available')->default(true)->after('available_for_bidding');
            
            // Performance metrics for bidding
            $table->integer('total_bid_deliveries')->default(0)->after('currently_available');
            $table->decimal('average_rating', 3, 2)->default(5.00)->after('total_bid_deliveries');
            $table->integer('completed_deliveries_count')->default(0)->after('average_rating');
            $table->integer('cancelled_deliveries_count')->default(0)->after('completed_deliveries_count');
            
            // Delivery preferences
            $table->decimal('max_delivery_distance', 8, 2)->nullable()->after('cancelled_deliveries_count')->comment('Maximum delivery distance in km');
            $table->json('preferred_delivery_types')->nullable()->after('max_delivery_distance')->comment('JSON array of preferred delivery types');
            $table->time('work_start_time')->nullable()->after('preferred_delivery_types');
            $table->time('work_end_time')->nullable()->after('work_start_time');
            
            // Vehicle information
            $table->string('vehicle_type')->nullable()->after('work_end_time');
            $table->string('vehicle_number')->nullable()->after('vehicle_type');
            $table->decimal('vehicle_capacity', 8, 2)->nullable()->after('vehicle_number')->comment('Vehicle capacity in kg');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('delivery_man', function (Blueprint $table) {
            $table->dropColumn([
                'current_lat',
                'current_lng',
                'last_location_update',
                'available_for_bidding',
                'currently_available',
                'total_bid_deliveries',
                'average_rating',
                'completed_deliveries_count',
                'cancelled_deliveries_count',
                'max_delivery_distance',
                'preferred_delivery_types',
                'work_start_time',
                'work_end_time',
                'vehicle_type',
                'vehicle_number',
                'vehicle_capacity'
            ]);
        });
    }
};
