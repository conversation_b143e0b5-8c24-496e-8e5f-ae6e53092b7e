<?php

namespace App\Models\Backend;

use App\Models\Backend\Superadmin\Plan;
use App\Models\Tenant;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class GeneralSettings extends Model
{
    use HasFactory, LogsActivity;


    protected $fillable = [
        'phone',
        'name',
        'tracking_id',
        'details',
        'prefix',
        'purchase_code',
        // Bidding fields
        'hub_id',
        'bidding_enabled',
        'auto_bid_enabled',
        'min_bid_amount',
        'max_bid_amount',
        'default_pickup_time',
        'bidding_commission_rate',
        'auto_accept_bids',
        'auto_accept_threshold',
        'bid_notifications_enabled',
        'notification_email',
        'notification_phone',
        'service_areas',
        'vehicle_types',
        'max_parcel_weight',
        'fragile_items_accepted',
        'liquid_items_accepted'
    ];

    public function getActivitylogOptions(): LogOptions
    {

        $logAttributes = [

            'phone',
            'name',
            'tracking_id',
            'details',
            'prefix'
        ];
        return LogOptions::defaults()
            ->useLogName('General Settings')
            ->logOnly($logAttributes)
            ->setDescriptionForEvent(fn(string $eventName) => "{$eventName}");
    }

    // Get single row in Upload table.
    public function rxlogo()
    {
        return $this->belongsTo(Upload::class, 'logo', 'id');
    }
    public function lightlogo()
    {
        return $this->belongsTo(Upload::class, 'light_logo', 'id');
    }
    public function rxfavicon()
    {
        return $this->belongsTo(Upload::class, 'favicon', 'id');
    }

    public function getLogoImageAttribute()
    {
        if (!empty($this->rxlogo->original['original']) && file_exists(public_path($this->rxlogo->original['original']))) {
            return static_asset($this->rxlogo->original['original']);
        }
        return static_asset('images/default/logo.png');
    }

    public function getPLogoImageAttribute()
    {
        if (!empty($this->rxlogo->original['original']) && file_exists(public_path($this->rxlogo->original['original']))) {
            return public_path($this->rxlogo->original['original']);
        }
        return public_path('images/default/logo.png');
    }

    public function getLightLogoImageAttribute()
    {
        if (!empty($this->lightlogo->original['original']) && file_exists(public_path($this->lightlogo->original['original']))) {
            return static_asset($this->lightlogo->original['original']);
        }
        return static_asset('images/default/light-logo.png');
    }

    public function getFaviconImageAttribute()
    {
        if (!empty($this->rxfavicon->original['original']) && file_exists(public_path($this->rxfavicon->original['original']))) {
            return static_asset($this->rxfavicon->original['original']);
        }
        return static_asset('images/default/favicon.png');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by', 'id');
    }

    public function excenseRate()
    {
        return $this->belongsTo(Currency::class, 'currency', 'symbol');
    }

    public function plan()
    {
        return $this->belongsTo(Plan::class, 'plan_id', 'id');
    }

    public function subscription()
    {
        return $this->belongsTo(Subscription::class, 'subscription_id', 'id');
    }

    // Bidding relationships
    public function hub()
    {
        return $this->belongsTo(Hub::class, 'hub_id', 'id');
    }

    public function companyBids()
    {
        return $this->hasMany(CompanyBid::class, 'company_id', 'id');
    }

    public function wonParcels()
    {
        return $this->hasMany(Parcel::class, 'winning_company_id', 'id');
    }

    public function bidNotifications()
    {
        return $this->hasMany(BidNotification::class, 'company_id', 'id');
    }

    public function deliveryMen()
    {
        return $this->hasMany(DeliveryMan::class, 'company_id', 'id');
    }

    // Bidding accessors
    public function getBiddingEnabledAttribute($value)
    {
        return (bool) $value;
    }

    public function getAutoAcceptBidsAttribute($value)
    {
        return (bool) $value;
    }

    public function getServiceAreasAttribute($value)
    {
        return $value ? json_decode($value, true) : [];
    }

    public function setServiceAreasAttribute($value)
    {
        $this->attributes['service_areas'] = is_array($value) ? json_encode($value) : $value;
    }

    public function getVehicleTypesAttribute($value)
    {
        return $value ? json_decode($value, true) : [];
    }

    public function setVehicleTypesAttribute($value)
    {
        $this->attributes['vehicle_types'] = is_array($value) ? json_encode($value) : $value;
    }

    // Bidding business logic methods
    public function canBidOnParcel($parcel)
    {
        if (!$this->bidding_enabled) {
            return false;
        }

        // Check if company is in the same hub as parcel
        if ($this->hub_id !== $parcel->hub_id) {
            return false;
        }

        // Check if parcel can receive bids
        if (!$parcel->canReceiveBids()) {
            return false;
        }

        // Check if company already has a bid for this parcel
        if ($this->companyBids()->where('parcel_id', $parcel->id)->exists()) {
            return false;
        }

        return true;
    }

    public function getActiveBidsCount()
    {
        return $this->companyBids()->where('status', 'pending')->count();
    }

    public function getWonBidsCount()
    {
        return $this->companyBids()->where('status', 'accepted')->count();
    }

    public function getAvailableDeliveryMenCount()
    {
        return $this->deliveryMen()
            ->where('status', 1) // Active
            ->where('currently_available', true)
            ->count();
    }

    public function getBiddingSuccessRate()
    {
        $totalBids = $this->companyBids()->count();
        if ($totalBids === 0) {
            return 0;
        }

        $wonBids = $this->getWonBidsCount();
        return round(($wonBids / $totalBids) * 100, 2);
    }

    // Scopes
    public function scopeBiddingEnabled($query)
    {
        return $query->where('bidding_enabled', true);
    }

    public function scopeInHub($query, $hubId)
    {
        return $query->where('hub_id', $hubId);
    }

}
