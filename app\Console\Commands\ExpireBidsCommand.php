<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\HubBiddingService;
use App\Models\Backend\CompanyBid;
use App\Helpers\Bidding\BiddingSettingsHelper;
use Carbon\Carbon;

class ExpireBidsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bidding:expire-bids {--cleanup : Also cleanup old expired bids}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Expire bid parcels that have timed out and optionally cleanup old records';

    /**
     * Hub bidding service instance
     *
     * @var HubBiddingService
     */
    protected $hubBiddingService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(HubBiddingService $hubBiddingService)
    {
        parent::__construct();
        $this->hubBiddingService = $hubBiddingService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting bid expiration process...');

        try {
            // Expire timed out bid parcels
            $expiredCount = $this->hubBiddingService->expireBidParcels();
            $this->info("Expired {$expiredCount} bid parcels");

            // Mark individual expired bids
            $expiredBidsCount = CompanyBid::markExpiredBids();
            $this->info("Marked {$expiredBidsCount} individual bids as expired");

            // Cleanup old records if requested
            if ($this->option('cleanup')) {
                $cleanupCount = $this->cleanupOldBids();
                $this->info("Cleaned up {$cleanupCount} old bid records");
            }

            $this->info('Bid expiration process completed successfully');
            return 0;

        } catch (\Exception $e) {
            $this->error('Error during bid expiration: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * Cleanup old expired bid records
     *
     * @return int
     */
    private function cleanupOldBids()
    {
        $cleanupDays = BiddingSettingsHelper::get('bid_cleanup_days', 30);
        $cutoffDate = Carbon::now()->subDays($cleanupDays);

        // Delete old expired bids
        $deletedCount = CompanyBid::where('status', 'expired')
            ->where('updated_at', '<', $cutoffDate)
            ->delete();

        return $deletedCount;
    }
}
