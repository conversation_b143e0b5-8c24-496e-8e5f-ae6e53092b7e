<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('bid_notifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->constrained('general_settings')->onUpdate('cascade')->onDelete('cascade');
            $table->foreignId('parcel_id')->constrained('parcels')->onUpdate('cascade')->onDelete('cascade');
            $table->foreignId('company_bid_id')->nullable()->constrained('company_bids')->onUpdate('cascade')->onDelete('cascade');
            
            // Notification details
            $table->enum('notification_type', [
                'new_bid_available',
                'bid_accepted',
                'bid_rejected',
                'bid_expired',
                'bid_withdrawn',
                'parcel_cancelled',
                'new_bid_placed',
                'bid_deadline_reminder'
            ]);
            
            $table->string('title');
            $table->text('message');
            $table->json('data')->nullable()->comment('Additional notification data');
            
            // Delivery channels
            $table->boolean('sent_push')->default(false);
            $table->boolean('sent_email')->default(false);
            $table->boolean('sent_sms')->default(false);
            
            // Status tracking
            $table->timestamp('sent_at')->nullable();
            $table->timestamp('read_at')->nullable();
            $table->timestamp('clicked_at')->nullable();
            
            // Retry mechanism
            $table->integer('retry_count')->default(0);
            $table->timestamp('next_retry_at')->nullable();
            $table->text('error_message')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['company_id', 'read_at']);
            $table->index(['parcel_id', 'notification_type']);
            $table->index(['notification_type', 'sent_at']);
            $table->index('next_retry_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('bid_notifications');
    }
};
