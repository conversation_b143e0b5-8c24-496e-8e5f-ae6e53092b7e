<?php

namespace App\Http\Controllers\Api\V10;

use App\Http\Controllers\Controller;
use App\Models\Backend\Parcel;
use App\Models\Backend\CompanyBid;
use App\Models\Backend\GeneralSettings;
use App\Models\Backend\Hub;
use App\Services\HubBiddingService;
use App\Helpers\Bidding\CompanyBiddingHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class MerchantBiddingController extends Controller
{
    protected $hubBiddingService;

    public function __construct(HubBiddingService $hubBiddingService)
    {
        $this->hubBiddingService = $hubBiddingService;
    }

    /**
     * Create a bid parcel
     */
    public function createBidParcel(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'pickup_address' => 'required|string|max:255',
                'pickup_phone' => 'required|string|max:20',
                'customer_name' => 'required|string|max:100',
                'customer_phone' => 'required|string|max:20',
                'customer_address' => 'required|string|max:255',
                'weight' => 'required|numeric|min:0.1',
                'category_id' => 'required|exists:categories,id',
                'delivery_type_id' => 'required|exists:delivery_types,id',
                'offered_delivery_charge' => 'required|numeric|min:1',
                'bid_requirements' => 'nullable|string|max:500',
                'estimated_pickup_time' => 'nullable|integer|min:30|max:1440', // 30 minutes to 24 hours
                'minimum_bid_amount' => 'nullable|numeric|min:1',
                'maximum_bid_amount' => 'nullable|numeric|min:1',
                'pickup_date' => 'nullable|date|after_or_equal:today',
                'delivery_date' => 'nullable|date|after:pickup_date',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $merchant = Auth::user()->merchant;
            if (!$merchant) {
                return response()->json([
                    'success' => false,
                    'message' => 'Merchant not found'
                ], 404);
            }

            // Prepare parcel data
            $parcelData = $request->only([
                'pickup_address', 'pickup_phone', 'customer_name', 'customer_phone',
                'customer_address', 'weight', 'category_id', 'delivery_type_id',
                'offered_delivery_charge', 'bid_requirements', 'estimated_pickup_time',
                'minimum_bid_amount', 'maximum_bid_amount', 'pickup_date', 'delivery_date'
            ]);

            $parcelData['merchant_id'] = $merchant->id;
            $parcelData['merchant_shop_id'] = $merchant->shops()->first()->id ?? null;
            $parcelData['tracking_id'] = 'BP' . time() . rand(1000, 9999); // Bid Parcel prefix
            $parcelData['status'] = 'pending';

            // Create bid parcel through service
            $result = $this->hubBiddingService->createBidParcel($merchant->id, $parcelData);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => [
                        'parcel' => $result['parcel'],
                        'notified_companies' => $result['notified_companies']
                    ]
                ], 201);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message']
                ], 400);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create bid parcel: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get company bids for a parcel
     */
    public function getCompanyBids(Request $request, $parcelId)
    {
        try {
            $parcel = Parcel::with(['companyBids.company', 'companyBids' => function($query) {
                $query->orderBy('offered_charge', 'asc');
            }])->find($parcelId);

            if (!$parcel) {
                return response()->json([
                    'success' => false,
                    'message' => 'Parcel not found'
                ], 404);
            }

            // Verify merchant owns this parcel
            $merchant = Auth::user()->merchant;
            if ($parcel->merchant_id !== $merchant->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access to parcel'
                ], 403);
            }

            $bids = $parcel->companyBids->map(function($bid) {
                return [
                    'id' => $bid->id,
                    'company' => [
                        'id' => $bid->company->id,
                        'name' => $bid->company->name,
                        'phone' => $bid->company->phone,
                        'success_rate' => $bid->company->getBiddingSuccessRate()
                    ],
                    'offered_charge' => $bid->offered_charge,
                    'estimated_pickup_time' => $bid->estimated_pickup_time,
                    'estimated_delivery_time' => $bid->estimated_delivery_time,
                    'message' => $bid->message,
                    'contact_person' => $bid->contact_person,
                    'contact_phone' => $bid->contact_phone,
                    'service_features' => $bid->service_features,
                    'insurance_coverage' => $bid->insurance_coverage,
                    'priority_delivery' => $bid->priority_delivery,
                    'status' => $bid->status,
                    'created_at' => $bid->created_at,
                    'time_remaining' => $bid->time_remaining
                ];
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'parcel' => [
                        'id' => $parcel->id,
                        'tracking_id' => $parcel->tracking_id,
                        'offered_delivery_charge' => $parcel->offered_delivery_charge,
                        'bid_status' => $parcel->bid_status,
                        'bid_timeout_at' => $parcel->bid_timeout_at,
                        'active_bids_count' => $parcel->active_bids_count
                    ],
                    'bids' => $bids,
                    'statistics' => [
                        'total_bids' => $bids->count(),
                        'lowest_bid' => $bids->min('offered_charge'),
                        'highest_bid' => $bids->max('offered_charge'),
                        'average_bid' => $bids->avg('offered_charge')
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get company bids: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Accept a company bid
     */
    public function acceptCompanyBid(Request $request, $parcelId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'bid_id' => 'required|exists:company_bids,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $merchant = Auth::user()->merchant;
            $result = $this->hubBiddingService->acceptCompanyBid(
                $parcelId, 
                $request->bid_id, 
                $merchant->id
            );

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => [
                        'parcel' => $result['parcel'],
                        'winning_bid' => $result['winning_bid']
                    ]
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message']
                ], 400);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to accept company bid: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Cancel a bid parcel
     */
    public function cancelBidParcel(Request $request, $parcelId)
    {
        try {
            $parcel = Parcel::find($parcelId);
            if (!$parcel) {
                return response()->json([
                    'success' => false,
                    'message' => 'Parcel not found'
                ], 404);
            }

            $merchant = Auth::user()->merchant;
            if ($parcel->merchant_id !== $merchant->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access to parcel'
                ], 403);
            }

            if (!$parcel->is_bid_parcel || $parcel->bid_status !== 'open') {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot cancel this parcel'
                ], 400);
            }

            // Update parcel status
            $parcel->update([
                'bid_status' => 'expired',
                'status' => 'cancelled'
            ]);

            // Mark all pending bids as expired
            $parcel->companyBids()
                   ->where('status', 'pending')
                   ->update(['status' => 'expired']);

            return response()->json([
                'success' => true,
                'message' => 'Bid parcel cancelled successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to cancel bid parcel: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get companies in merchant's hub
     */
    public function getHubCompanies(Request $request, $hubId)
    {
        try {
            $companies = $this->hubBiddingService->getEligibleCompanies($hubId);

            $companiesData = $companies->map(function($company) {
                return [
                    'id' => $company->id,
                    'name' => $company->name,
                    'phone' => $company->phone,
                    'bidding_enabled' => $company->bidding_enabled,
                    'success_rate' => $company->getBiddingSuccessRate(),
                    'active_bids' => $company->getActiveBidsCount(),
                    'available_delivery_men' => $company->getAvailableDeliveryMenCount()
                ];
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'hub_id' => $hubId,
                    'companies' => $companiesData,
                    'total_companies' => $companiesData->count()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get hub companies: ' . $e->getMessage()
            ], 500);
        }
    }
}
