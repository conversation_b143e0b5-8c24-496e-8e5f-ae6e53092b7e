<?php

namespace App\Helpers\Bidding;

use App\Models\Backend\Parcel;
use App\Models\Backend\CompanyBid;
use App\Models\Backend\BidNotification;
use App\Models\Backend\GeneralSettings;
use Illuminate\Support\Facades\Log;

class BiddingNotificationHelper
{
    /**
     * Send notification to companies about new bid opportunity
     */
    public function sendNewBidNotification($company, $parcel)
    {
        try {
            if (!$company->bid_notifications_enabled) {
                return false;
            }

            $title = 'New Bid Opportunity';
            $message = "New parcel available for bidding: {$parcel->tracking_id}";
            $data = [
                'type' => 'new_bid_available',
                'parcel_id' => $parcel->id,
                'tracking_id' => $parcel->tracking_id,
                'pickup_address' => $parcel->pickup_address,
                'delivery_address' => $parcel->customer_address,
                'offered_charge' => $parcel->offered_delivery_charge,
                'weight' => $parcel->weight,
                'bid_timeout' => $parcel->bid_timeout_at
            ];

            // Send push notification if enabled
            if ($company->notification_email) {
                $this->sendEmailNotification($company->notification_email, $title, $message, $data);
            }

            // Send SMS if phone number available
            if ($company->notification_phone) {
                $this->sendSMSNotification($company->notification_phone, $message);
            }

            // Send push notification to company app
            $this->sendPushNotification($company->id, $title, $message, $data);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to send new bid notification: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Notify merchant about new bid placed
     */
    public function notifyMerchantOfNewBid($parcel, $bid)
    {
        try {
            $merchant = $parcel->merchant;
            if (!$merchant) {
                return false;
            }

            $title = 'New Bid Received';
            $message = "Company {$bid->company->name} placed a bid of {$bid->offered_charge} for parcel {$parcel->tracking_id}";
            
            $data = [
                'type' => 'new_bid_placed',
                'parcel_id' => $parcel->id,
                'bid_id' => $bid->id,
                'company_name' => $bid->company->name,
                'offered_charge' => $bid->offered_charge,
                'estimated_pickup_time' => $bid->estimated_pickup_time
            ];

            // Create notification record
            BidNotification::create([
                'company_id' => $bid->company_id,
                'parcel_id' => $parcel->id,
                'company_bid_id' => $bid->id,
                'notification_type' => 'new_bid_placed',
                'title' => $title,
                'message' => $message,
                'data' => $data
            ]);

            // Send to merchant
            $this->sendMerchantNotification($merchant, $title, $message, $data);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to notify merchant of new bid: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Notify company that their bid was accepted
     */
    public function notifyBidAccepted($bid)
    {
        try {
            $company = $bid->company;
            $parcel = $bid->parcel;

            $title = 'Bid Accepted!';
            $message = "Congratulations! Your bid of {$bid->offered_charge} for parcel {$parcel->tracking_id} has been accepted.";
            
            $data = [
                'type' => 'bid_accepted',
                'parcel_id' => $parcel->id,
                'bid_id' => $bid->id,
                'accepted_charge' => $bid->offered_charge,
                'pickup_address' => $parcel->pickup_address,
                'delivery_address' => $parcel->customer_address
            ];

            // Create notification record
            BidNotification::createForBidAccepted($bid);

            // Send notifications
            if ($company->notification_email) {
                $this->sendEmailNotification($company->notification_email, $title, $message, $data);
            }

            if ($company->notification_phone) {
                $this->sendSMSNotification($company->notification_phone, $message);
            }

            $this->sendPushNotification($company->id, $title, $message, $data);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to notify bid accepted: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Notify other companies that they lost the bid
     */
    public function notifyOtherCompaniesOfBidLoss($parcel, $winningBid)
    {
        try {
            $losingBids = $parcel->companyBids()
                ->where('id', '!=', $winningBid->id)
                ->where('status', 'pending')
                ->get();

            foreach ($losingBids as $bid) {
                $company = $bid->company;
                
                $title = 'Bid Not Selected';
                $message = "Your bid for parcel {$parcel->tracking_id} was not selected. The winning bid was {$winningBid->offered_charge}.";
                
                $data = [
                    'type' => 'bid_rejected',
                    'parcel_id' => $parcel->id,
                    'bid_id' => $bid->id,
                    'your_bid' => $bid->offered_charge,
                    'winning_bid' => $winningBid->offered_charge
                ];

                // Create notification record
                BidNotification::createForBidRejected($bid);

                // Send notifications
                if ($company->notification_email) {
                    $this->sendEmailNotification($company->notification_email, $title, $message, $data);
                }

                $this->sendPushNotification($company->id, $title, $message, $data);
            }

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to notify bid loss: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Notify about expired bids
     */
    public function notifyBidExpired($parcel)
    {
        try {
            $expiredBids = $parcel->companyBids()
                ->where('status', 'pending')
                ->get();

            foreach ($expiredBids as $bid) {
                $company = $bid->company;
                
                $title = 'Bid Expired';
                $message = "The bidding period for parcel {$parcel->tracking_id} has expired.";
                
                $data = [
                    'type' => 'bid_expired',
                    'parcel_id' => $parcel->id,
                    'bid_id' => $bid->id
                ];

                // Create notification record
                BidNotification::create([
                    'company_id' => $company->id,
                    'parcel_id' => $parcel->id,
                    'company_bid_id' => $bid->id,
                    'notification_type' => 'bid_expired',
                    'title' => $title,
                    'message' => $message,
                    'data' => $data
                ]);

                $this->sendPushNotification($company->id, $title, $message, $data);
            }

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to notify bid expired: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Send email notification
     */
    private function sendEmailNotification($email, $title, $message, $data)
    {
        // Implement email sending logic here
        // This could use Laravel's Mail facade or a third-party service
        Log::info("Email notification sent to {$email}: {$title}");
    }

    /**
     * Send SMS notification
     */
    private function sendSMSNotification($phone, $message)
    {
        // Implement SMS sending logic here
        // This could use Twilio, Nexmo, or other SMS services
        Log::info("SMS notification sent to {$phone}: {$message}");
    }

    /**
     * Send push notification
     */
    private function sendPushNotification($companyId, $title, $message, $data)
    {
        // Implement push notification logic here
        // This could use Firebase Cloud Messaging or other push services
        Log::info("Push notification sent to company {$companyId}: {$title}");
    }

    /**
     * Send notification to merchant
     */
    private function sendMerchantNotification($merchant, $title, $message, $data)
    {
        // Implement merchant notification logic here
        // This could be email, SMS, or push notification to merchant app
        Log::info("Merchant notification sent to {$merchant->id}: {$title}");
    }

    /**
     * Send bid deadline reminder
     */
    public function sendBidDeadlineReminder($parcel, $hoursRemaining = 2)
    {
        try {
            $companies = GeneralSettings::where('hub_id', $parcel->hub_id)
                ->where('bidding_enabled', true)
                ->get();

            foreach ($companies as $company) {
                // Check if company hasn't bid yet
                $hasBid = $company->companyBids()
                    ->where('parcel_id', $parcel->id)
                    ->exists();

                if (!$hasBid) {
                    $title = 'Bid Deadline Reminder';
                    $message = "Only {$hoursRemaining} hours left to bid on parcel {$parcel->tracking_id}";
                    
                    $data = [
                        'type' => 'bid_deadline_reminder',
                        'parcel_id' => $parcel->id,
                        'hours_remaining' => $hoursRemaining
                    ];

                    BidNotification::create([
                        'company_id' => $company->id,
                        'parcel_id' => $parcel->id,
                        'notification_type' => 'bid_deadline_reminder',
                        'title' => $title,
                        'message' => $message,
                        'data' => $data
                    ]);

                    $this->sendPushNotification($company->id, $title, $message, $data);
                }
            }

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to send bid deadline reminder: ' . $e->getMessage());
            return false;
        }
    }
}
