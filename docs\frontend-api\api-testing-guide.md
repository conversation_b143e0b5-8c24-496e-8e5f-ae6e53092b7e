# API Testing Guide - Bidding System

## Overview
This document provides comprehensive testing scenarios, sample requests/responses, and testing strategies for the WeCourier bidding system APIs.

## Testing Environment Setup

### Environment Configuration
```javascript
// config/test.js
export const TEST_CONFIG = {
    API_BASE_URL: 'https://staging-api.wecourier.com/api/v10',
    WS_URL: 'wss://staging-ws.wecourier.com',
    TEST_API_KEY: 'test_api_key_12345',
    TEST_USERS: {
        merchant: {
            id: 1,
            email: '<EMAIL>',
            token: 'merchant_test_token'
        },
        company: {
            id: 1,
            email: '<EMAIL>',
            token: 'company_test_token'
        },
        deliveryman: {
            id: 1,
            email: '<EMAIL>',
            token: 'deliveryman_test_token'
        },
        admin: {
            id: 1,
            email: '<EMAIL>',
            token: 'admin_test_token'
        }
    }
};
```

### Test Data Setup
```javascript
// testData/biddingTestData.js
export const TEST_DATA = {
    bidParcel: {
        merchant_shop_id: 1,
        pickup_address: "123 Test Street, Test City",
        pickup_phone: "+1234567890",
        customer_name: "John Doe",
        customer_phone: "+0987654321",
        customer_address: "456 Customer Ave, Customer City",
        invoice_no: "INV-TEST-001",
        category_id: 1,
        weight: 2.5,
        delivery_type_id: 1,
        cash_collection: 100.00,
        note: "Test parcel for bidding",
        is_bid_parcel: 1,
        offered_delivery_charge: 25.00,
        minimum_bid_amount: 15.00,
        maximum_bid_amount: 30.00,
        bid_requirements: "Handle with care",
        estimated_pickup_time: 60,
        bid_timeout_hours: 24
    },
    companyBid: {
        offered_charge: 20.00,
        estimated_pickup_time: 45,
        estimated_delivery_time: 120,
        message: "We can deliver this quickly and safely",
        contact_person: "Test Driver",
        contact_phone: "+1122334455",
        service_features: ["GPS Tracking", "SMS Updates"],
        insurance_coverage: 500.00,
        priority_delivery: false
    }
};
```

## API Testing Scenarios

### 1. Merchant Bidding Flow Tests

#### Test Case 1: Create Bid Parcel
```javascript
// tests/merchant/createBidParcel.test.js
describe('Create Bid Parcel', () => {
    test('should create bid parcel successfully', async () => {
        const response = await fetch(`${TEST_CONFIG.API_BASE_URL}/merchant/bidding/parcel/create`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${TEST_CONFIG.TEST_USERS.merchant.token}`,
                'X-API-KEY': TEST_CONFIG.TEST_API_KEY
            },
            body: JSON.stringify(TEST_DATA.bidParcel)
        });

        const result = await response.json();

        expect(response.status).toBe(201);
        expect(result.success).toBe(true);
        expect(result.data.parcel).toBeDefined();
        expect(result.data.parcel.is_bid_parcel).toBe(true);
        expect(result.data.parcel.bid_status).toBe('open');
        expect(result.data.notified_companies).toBeGreaterThan(0);
    });

    test('should fail with invalid bid amount', async () => {
        const invalidData = {
            ...TEST_DATA.bidParcel,
            offered_delivery_charge: -10 // Invalid negative amount
        };

        const response = await fetch(`${TEST_CONFIG.API_BASE_URL}/merchant/bidding/parcel/create`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${TEST_CONFIG.TEST_USERS.merchant.token}`,
                'X-API-KEY': TEST_CONFIG.TEST_API_KEY
            },
            body: JSON.stringify(invalidData)
        });

        const result = await response.json();

        expect(response.status).toBe(422);
        expect(result.success).toBe(false);
        expect(result.errors).toBeDefined();
    });
});
```

#### Test Case 2: Get Company Bids
```javascript
// tests/merchant/getCompanyBids.test.js
describe('Get Company Bids', () => {
    let testParcelId;

    beforeEach(async () => {
        // Create a test parcel first
        const createResponse = await createTestBidParcel();
        testParcelId = createResponse.data.parcel.id;
        
        // Wait for companies to place bids
        await waitForBids(testParcelId, 2); // Wait for at least 2 bids
    });

    test('should get all bids for parcel', async () => {
        const response = await fetch(
            `${TEST_CONFIG.API_BASE_URL}/merchant/bidding/parcel/${testParcelId}/company-bids`,
            {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${TEST_CONFIG.TEST_USERS.merchant.token}`,
                    'X-API-KEY': TEST_CONFIG.TEST_API_KEY
                }
            }
        );

        const result = await response.json();

        expect(response.status).toBe(200);
        expect(result.success).toBe(true);
        expect(result.data.bids).toBeInstanceOf(Array);
        expect(result.data.bids.length).toBeGreaterThan(0);
        expect(result.data.statistics).toBeDefined();
    });

    test('should filter bids by status', async () => {
        const response = await fetch(
            `${TEST_CONFIG.API_BASE_URL}/merchant/bidding/parcel/${testParcelId}/company-bids?status=pending`,
            {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${TEST_CONFIG.TEST_USERS.merchant.token}`,
                    'X-API-KEY': TEST_CONFIG.TEST_API_KEY
                }
            }
        );

        const result = await response.json();

        expect(response.status).toBe(200);
        expect(result.data.bids.every(bid => bid.status === 'pending')).toBe(true);
    });
});
```

### 2. Company Bidding Flow Tests

#### Test Case 3: Get Available Parcels
```javascript
// tests/company/getAvailableParcels.test.js
describe('Get Available Parcels', () => {
    test('should get available parcels for bidding', async () => {
        const response = await fetch(
            `${TEST_CONFIG.API_BASE_URL}/company/bidding/available-parcels`,
            {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${TEST_CONFIG.TEST_USERS.company.token}`,
                    'X-API-KEY': TEST_CONFIG.TEST_API_KEY
                }
            }
        );

        const result = await response.json();

        expect(response.status).toBe(200);
        expect(result.success).toBe(true);
        expect(result.data.parcels).toBeInstanceOf(Array);
        expect(result.data.pagination).toBeDefined();
    });

    test('should filter parcels by category', async () => {
        const response = await fetch(
            `${TEST_CONFIG.API_BASE_URL}/company/bidding/available-parcels?category_id=1`,
            {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${TEST_CONFIG.TEST_USERS.company.token}`,
                    'X-API-KEY': TEST_CONFIG.TEST_API_KEY
                }
            }
        );

        const result = await response.json();

        expect(response.status).toBe(200);
        expect(result.data.parcels.every(parcel => parcel.category_id === 1)).toBe(true);
    });
});
```

#### Test Case 4: Place Bid
```javascript
// tests/company/placeBid.test.js
describe('Place Bid', () => {
    let testParcelId;

    beforeEach(async () => {
        const availableParcels = await getAvailableParcels();
        testParcelId = availableParcels.data.parcels[0].id;
    });

    test('should place bid successfully', async () => {
        const response = await fetch(
            `${TEST_CONFIG.API_BASE_URL}/company/bidding/parcel/${testParcelId}/bid`,
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${TEST_CONFIG.TEST_USERS.company.token}`,
                    'X-API-KEY': TEST_CONFIG.TEST_API_KEY
                },
                body: JSON.stringify(TEST_DATA.companyBid)
            }
        );

        const result = await response.json();

        expect(response.status).toBe(201);
        expect(result.success).toBe(true);
        expect(result.data.bid).toBeDefined();
        expect(result.data.bid.status).toBe('pending');
        expect(result.data.bid.offered_charge).toBe(TEST_DATA.companyBid.offered_charge);
    });

    test('should fail with bid amount outside constraints', async () => {
        const invalidBid = {
            ...TEST_DATA.companyBid,
            offered_charge: 5.00 // Too low
        };

        const response = await fetch(
            `${TEST_CONFIG.API_BASE_URL}/company/bidding/parcel/${testParcelId}/bid`,
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${TEST_CONFIG.TEST_USERS.company.token}`,
                    'X-API-KEY': TEST_CONFIG.TEST_API_KEY
                },
                body: JSON.stringify(invalidBid)
            }
        );

        const result = await response.json();

        expect(response.status).toBe(422);
        expect(result.success).toBe(false);
        expect(result.message).toContain('bid amount');
    });
});
```

### 3. Integration Tests

#### Test Case 5: Complete Bidding Flow
```javascript
// tests/integration/completeBiddingFlow.test.js
describe('Complete Bidding Flow', () => {
    test('should complete full bidding cycle', async () => {
        // Step 1: Merchant creates bid parcel
        const createResponse = await createTestBidParcel();
        const parcelId = createResponse.data.parcel.id;

        // Step 2: Companies place bids
        const bid1 = await placeBid(parcelId, { ...TEST_DATA.companyBid, offered_charge: 22.00 });
        const bid2 = await placeBid(parcelId, { ...TEST_DATA.companyBid, offered_charge: 18.00 });

        // Step 3: Merchant accepts best bid
        const acceptResponse = await acceptBid(parcelId, bid2.data.bid.id);
        expect(acceptResponse.success).toBe(true);

        // Step 4: Company assigns delivery man
        const assignResponse = await assignDeliveryman(parcelId, 1);
        expect(assignResponse.success).toBe(true);

        // Step 5: Verify parcel status
        const parcelDetails = await getParcelDetails(parcelId);
        expect(parcelDetails.data.bid_status).toBe('assigned');
        expect(parcelDetails.data.assigned_deliveryman_id).toBe(1);
    });
});
```

## Performance Testing

### Load Testing Scenarios
```javascript
// tests/performance/loadTest.js
describe('Load Testing', () => {
    test('should handle concurrent bid placements', async () => {
        const parcelId = await createTestBidParcel();
        const concurrentBids = 10;
        
        const bidPromises = Array.from({ length: concurrentBids }, (_, index) => 
            placeBid(parcelId, {
                ...TEST_DATA.companyBid,
                offered_charge: 20.00 + index,
                contact_person: `Test Driver ${index}`
            })
        );

        const results = await Promise.allSettled(bidPromises);
        
        // At least one bid should succeed
        const successfulBids = results.filter(result => 
            result.status === 'fulfilled' && result.value.success
        );
        
        expect(successfulBids.length).toBeGreaterThan(0);
    });

    test('should handle high frequency parcel creation', async () => {
        const concurrentParcels = 5;
        const startTime = Date.now();
        
        const parcelPromises = Array.from({ length: concurrentParcels }, (_, index) =>
            createTestBidParcel({
                ...TEST_DATA.bidParcel,
                invoice_no: `INV-LOAD-${index}`
            })
        );

        const results = await Promise.all(parcelPromises);
        const endTime = Date.now();
        
        expect(results.every(result => result.success)).toBe(true);
        expect(endTime - startTime).toBeLessThan(10000); // Should complete within 10 seconds
    });
});
```

## WebSocket Testing

### Real-time Event Testing
```javascript
// tests/websocket/realtimeEvents.test.js
describe('WebSocket Real-time Events', () => {
    let wsConnection;

    beforeEach(async () => {
        wsConnection = new WebSocket(`${TEST_CONFIG.WS_URL}/ws/company/1`);
        await waitForConnection(wsConnection);
    });

    afterEach(() => {
        wsConnection.close();
    });

    test('should receive new bid opportunity notification', (done) => {
        wsConnection.onmessage = (event) => {
            const data = JSON.parse(event.data);
            
            if (data.type === 'new_bid_opportunity') {
                expect(data.payload.parcel).toBeDefined();
                expect(data.payload.parcel.is_bid_parcel).toBe(true);
                done();
            }
        };

        // Trigger event by creating a new bid parcel
        createTestBidParcel();
    });

    test('should receive bid acceptance notification', (done) => {
        wsConnection.onmessage = (event) => {
            const data = JSON.parse(event.data);
            
            if (data.type === 'bid_accepted') {
                expect(data.payload.bid).toBeDefined();
                expect(data.payload.bid.status).toBe('accepted');
                done();
            }
        };

        // Trigger event by accepting a bid
        acceptTestBid();
    });
});
```

## Test Utilities

### Helper Functions
```javascript
// tests/utils/testHelpers.js
export const createTestBidParcel = async (customData = {}) => {
    const parcelData = { ...TEST_DATA.bidParcel, ...customData };
    
    const response = await fetch(`${TEST_CONFIG.API_BASE_URL}/merchant/bidding/parcel/create`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${TEST_CONFIG.TEST_USERS.merchant.token}`,
            'X-API-KEY': TEST_CONFIG.TEST_API_KEY
        },
        body: JSON.stringify(parcelData)
    });

    return await response.json();
};

export const placeBid = async (parcelId, bidData = TEST_DATA.companyBid) => {
    const response = await fetch(
        `${TEST_CONFIG.API_BASE_URL}/company/bidding/parcel/${parcelId}/bid`,
        {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${TEST_CONFIG.TEST_USERS.company.token}`,
                'X-API-KEY': TEST_CONFIG.TEST_API_KEY
            },
            body: JSON.stringify(bidData)
        }
    );

    return await response.json();
};

export const waitForBids = async (parcelId, minBids = 1, timeout = 30000) => {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
        const bidsResponse = await fetch(
            `${TEST_CONFIG.API_BASE_URL}/merchant/bidding/parcel/${parcelId}/company-bids`,
            {
                headers: {
                    'Authorization': `Bearer ${TEST_CONFIG.TEST_USERS.merchant.token}`,
                    'X-API-KEY': TEST_CONFIG.TEST_API_KEY
                }
            }
        );
        
        const bidsResult = await bidsResponse.json();
        
        if (bidsResult.data.bids.length >= minBids) {
            return bidsResult.data.bids;
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    throw new Error(`Timeout waiting for ${minBids} bids`);
};

export const waitForConnection = (ws) => {
    return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
            reject(new Error('WebSocket connection timeout'));
        }, 5000);

        ws.onopen = () => {
            clearTimeout(timeout);
            resolve();
        };

        ws.onerror = (error) => {
            clearTimeout(timeout);
            reject(error);
        };
    });
};
```

### Test Configuration
```javascript
// jest.config.js
module.exports = {
    testEnvironment: 'node',
    setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],
    testMatch: ['<rootDir>/tests/**/*.test.js'],
    collectCoverageFrom: [
        'src/**/*.js',
        '!src/index.js',
        '!src/config/**'
    ],
    coverageThreshold: {
        global: {
            branches: 80,
            functions: 80,
            lines: 80,
            statements: 80
        }
    },
    testTimeout: 30000
};
```

This completes the comprehensive API testing documentation for the WeCourier bidding system frontend integration.
